###
 # @Author: sun<PERSON><PERSON><PERSON>e <EMAIL>
 # @Date: 2023-12-21 12:05:23
 # @LastEditors: Please set LastEditors
 # @LastEditTime: 2024-12-31 11:07:59
 # @FilePath: \dd-mini-operation-saas-park-map\.env.development
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 
# 页面标题
VUE_APP_TITLE = Saas后台管理系统

# 开发环境配置
ENV = 'development'

# 后台管理系统/开发环境
#  VUE_APP_BASE_API = 'https://test.dreeck.com:11443'
VUE_APP_BASE_API = 'https://www.dreamdeck.cn:10443'
# VUE_APP_BASE_API='https://saastest.dreamdeck.cn'
# VUE_APP_BASE_API = 'https://ticket.bjyytpark.com:10443'
# VUE_APP_BASE_API = 'http://***********:9999'
# 路由懒加载
VUE_CLI_BABEL_TRANSPILE_MODULES = true
