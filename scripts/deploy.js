const path = require('path')
const { NodeSSH } = require('node-ssh')
const ssh = new NodeSSH()
const fs = require('fs')
const ora = require('ora')
const chalk = require('chalk')
const { exec } = require('child_process')

// 获取环境参数
const env = process.argv[2] || 'development'

// 读取配置文件
let config;
try {
  config = require('../deploy.config.json');
  console.log('当前环境:', env);
  console.log('加载的配置:', config);
} catch (error) {
  console.error(chalk.red('无法读取配置文件 deploy.config.json'));
  console.error(error);
  process.exit(1);
}

const envConfig = config[env];

// 验证配置
if (!envConfig) {
  console.error(chalk.red(`未找到环境 ${env} 的配置`));
  process.exit(1);
}

if (!envConfig.targetPath) {
  console.error(chalk.red(`环境 ${env} 的 targetPath 未配置`));
  process.exit(1);
}

async function deploy() {
  const spinner = ora('开始部署...\n').start();
  
  try {
    // 1. 连接服务器
    spinner.text = '正在连接服务器...';
    if (!envConfig.host || !envConfig.username || !envConfig.password) {
      throw new Error('服务器配置信息不完整，请检查 host、username 和 password');
    }

    await ssh.connect({
      host: envConfig.host,
      username: envConfig.username,
      password: envConfig.password,
      port: envConfig.port || 22
    });
    
    // 2. 执行构建
    spinner.text = '正在构建项目...';
    await execLocal('npm run build');
    
    // 3. 上传文件
    spinner.text = '正在上传文件...';
    const localPath = path.resolve(process.cwd(), 'dist');
    const remotePath = envConfig.targetPath;

    // 输出调试信息
    console.log('部署配置:', {
      环境: env,
      本地路径: localPath,
      远程路径: remotePath,
      服务器: `${envConfig.username}@${envConfig.host}:${envConfig.port || 22}`
    });
    
    if (!fs.existsSync(localPath)) {
      throw new Error(`本地构建目录不存在: ${localPath}`);
    }

    await ssh.putDirectory(localPath, remotePath, {
      recursive: true,
      concurrency: 10,
      validate: (itemPath) => {
        const baseName = path.basename(itemPath);
        return baseName.charAt(0) !== '.' && 
               baseName !== 'node_modules';
      },
      tick: (localPath, remotePath, error) => {
        if (error) {
          console.error('上传失败:', localPath, error);
        }
      }
    });

    spinner.succeed(chalk.green(`成功部署到 ${env} 环境！`));
  } catch (error) {
    spinner.fail(chalk.red('部署失败：' + error.message));
    console.error('详细错误信息:', error);
    process.exit(1);
  } finally {
    ssh.dispose();
  }
}

function execLocal(command) {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        reject(error);
      } else {
        resolve(stdout);
      }
    });
  });
}

deploy()
