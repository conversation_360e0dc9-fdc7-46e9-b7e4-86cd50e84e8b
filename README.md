# dd-mini-greenheart-map

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```

### Lints and fixes files
```
npm run lint
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).


navigator.mediaDevices.getUserMedia({
      audio: true,
      // 设置音频格式
      mimeType: 'audio/pcm; codecs=opus',
      // 设置采样率
      sampleRate: 16000,
      // 其他音频选项
      channelCount: 1, // 声道数
      echoCancellation: true, // 回声消除
    })
      .then(async (stream) => {
        console.log('获取麦克风权限成功', stream);
        this.mediaRecorder = new MediaRecorder(stream);
        // 3. 设置 ondataavailable 事件
        this.mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            this.recordedChunks.push(event.data);
          }
          console.log('获取音频数据', event.data);
          // 利用AudioWorkletNode读取音频数据
          const audioContext = new (window.AudioContext || window.webkitAudioContext)();
          audioContext.audioWorklet.addModule('./my-worklet-processor.js').then(() => {
            const audioWorkletNode = new AudioWorkletNode(audioContext, 'myProcessor');
            // 创建一个音频源节点
            const mediaStreamSource = audioContext.createMediaStreamSource(stream);
            mediaStreamSource.connect(audioWorkletNode);
            audioWorkletNode.port.onmessage = (event) => {
              const processedData = event.data;
            }
          })
        };
      })
      .catch(function (err) {
        console.error('无法获取麦克风权限或设备：', err);
      });