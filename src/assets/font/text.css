@font-face {
    font-family: "PingFang SC-Bold";
    /* 这里的字体名称是自定义的 */
    src: url("https://www.dreamdeck.cn:10443/app/icons/sass/font/PingFang SC-Bold.ttf");
}



@font-face {
    font-family: "PingFang SC-ExtraLight";
    /* 这里的字体名称是自定义的 */
    src: url("https://www.dreamdeck.cn:10443/app/icons/sass/font/PingFang SC-ExtraLight.ttf");
}

@font-face {
    font-family: "PingFang SC-Heavy";
    /* 这里的字体名称是自定义的 */
    src: url("https://www.dreamdeck.cn:10443/app/icons/sass/font/PingFang SC-Heavy.ttf");
}

@font-face {
    font-family: "PingFang SC-Light";
    /* 这里的字体名称是自定义的 */
    src: url("https://www.dreamdeck.cn:10443/app/icons/sass/font/PingFang SC-Light.ttf");
}
@font-face {
    font-family: "PingFang SC-Medium";
    /* 这里的字体名称是自定义的 */
    src: url("https://www.dreamdeck.cn:10443/app/icons/sass/font/PingFang SC-Medium.ttf");
}
@font-face {
    font-family: "PingFang SC-Regular";
    /* 这里的字体名称是自定义的 */
    src: url("https://www.dreamdeck.cn:10443/app/icons/sass/font/PingFang SC-Regular.ttf");
}
@font-face {
    font-family: "SourceHanSansCN SC-Bold";
    /* 这里的字体名称是自定义的 */
    src: url("https://www.dreamdeck.cn:10443/app/icons/sass/font/SourceHanSansCN-Bold.otf");
}
@font-face {
    font-family: "SourceHanSansCN-Regular";
    /* 这里的字体名称是自定义的 */
    src: url("https://www.dreamdeck.cn:10443/app/icons/sass/font/SourceHanSerifCN-Regular-1.otf");
}
@font-face {
    font-family: "NotoSansCJK-Bold";
    /* 这里的字体名称是自定义的 */
    src: url("https://www.dreamdeck.cn:10443/app/icons/sass/font/NotoSansCJK-Bold-6.ttf;");
}
@font-face {
    font-family: "NotoSansCJK-DemiLight";
    /* 这里的字体名称是自定义的 */
    src: url("https://www.dreamdeck.cn:10443/app/icons/sass/font/NotoSansCJK-DemiLight-4.ttf");
}
@font-face {
    font-family: "NotoSansCJK-Medium";
    /* 这里的字体名称是自定义的 */
    src: url("https://www.dreamdeck.cn:10443/app/icons/sass/font/NotoSansCJK-Medium-5.ttf");
}
@font-face {
    font-family: "NotoSansCJK-Regular";
    /* 这里的字体名称是自定义的 */
    src: url("https://www.dreamdeck.cn:10443/app/icons/sass/font/NotoSansCJK-Regular-1.ttf");
}