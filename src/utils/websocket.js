import ReconnectingWebSocket from 'reconnecting-websocket';

class websocketUtil {
  constructor(url, time, header = {}, ping = 'pong') {
    this.is_open_socket = false // 避免重复连接
    this.url = url // 地址
    this._header = header
    this._ping = ping
    this.data = null
    this.socketTask = null
    
    // 心跳检测
    this.timeout = time // 多少秒执行检测
    this.heartbeatInterval = null // 检测服务器端是否还活着
    this.reconnectTimeOut = null // 重连之后多久再次重连
    this.reconnectNum = 0 // 重连次数
    this.messageCallback = null
    
    try {
      return this.connectSocketInit()
    } catch (e) {
      console.error('初始化WebSocket失败:', e)
      this.is_open_socket = false
      this.reconnect();
    }
  }

  // 创建WebSocket连接
  connectSocketInit() {
    try {
      console.log('正在创建WebSocket连接:', this.url);
      
      // 配置reconnecting-websocket选项
      const options = {
        // WebSocket构造函数选项
        connectionTimeout: 5000,
        // 重连选项
        maxRetries: 10,
        reconnectionDelayGrowFactor: 1.3,
        maxReconnectionDelay: 10000,
        minReconnectionDelay: 1000,
        // 自定义WebSocket构造函数
        constructor: typeof WebSocket !== 'undefined' ? WebSocket : null
      };

      // 创建WebSocket连接
      this.socketTask = new ReconnectingWebSocket(this.url, [], options);

      // 打开连接事件
      this.socketTask.onopen = (event) => {
        console.log("WebSocket连接成功打开:", event);
        clearTimeout(this.reconnectTimeOut);
        clearTimeout(this.heartbeatInterval);
        this.is_open_socket = true;
        this.reconnectNum = 0;
        this.start();
      };

      // 接收消息事件
      this.socketTask.onmessage = (event) => {
        // console.log("收到WebSocket消息:", event.data);
        if (this.messageCallback && event.data) {
          const data = event.data;
          // 如果是心跳检测消息（ping/pong），不触发回调
          const isPingPong = typeof data === 'string' && (data.includes('pong') || data.includes('ping'));
          const isSuccessful = typeof data === 'string' && data.includes('successful');
          
          if (isPingPong) {
            // console.log('收到心跳消息:', data);
          } else if (isSuccessful) {
            console.log('连接成功消息:', data);
          } else {
            // 触发回调处理其他消息
            this.messageCallback(event);
          }
        }
      };

      // 关闭连接事件
      this.socketTask.onclose = (event) => {
        console.log("WebSocket连接已关闭:", event);
        this.is_open_socket = false;
        
        // 如果不是正常关闭，尝试重连
        if (event.code !== 1000) {
          this.reconnect();
        }
      };

      // 连接错误事件
      this.socketTask.onerror = (error) => {
        console.error("WebSocket连接错误:", error);
        this.is_open_socket = false;
        // 错误时自动尝试重连
        this.reconnect();
      };

      return this;
    } catch (error) {
      console.error("创建WebSocket连接失败:", error);
      this.is_open_socket = false;
      this.reconnect();
      return this;
    }
  }

  // 发送消息
  send(value) {
    if (this.socketTask && this.is_open_socket && this.socketTask.readyState === WebSocket.OPEN) {
      try {
        // 转换为字符串
        const message = typeof value === 'object' ? JSON.stringify(value) : value;
        this.socketTask.send(message);
        // console.log("WebSocket消息发送成功:", message);
        return true;
      } catch (error) {
        console.error("发送WebSocket消息失败:", error);
        return false;
      }
    } else {
      console.warn("WebSocket未连接或未就绪，无法发送消息");
      // 如果连接未建立，尝试重新连接
      if (!this.is_open_socket) {
        this.reconnect();
      }
      return false;
    }
  }

  // 开启心跳检测
  start() {
    this.data = { type: this._ping };
    this.send(this.data);
    
    // 设置定时心跳
    this.heartbeatInterval = setInterval(() => {
      this.send(this.data);
    }, this.timeout * 1000);
  }

  // 重连机制
  reconnect() {
    this.reconnectNum++;
    
    if (this.reconnectNum > 3) {
      // 重连超过3次，提示用户
      if (typeof window !== 'undefined') {
        if (confirm('连接对话服务失败,是否继续尝试重连?')) {
          this.reconnectNum = 0;
          this.connectSocketInit();
        } else {
          // 用户取消重连，可以执行其他操作
          if (window.history.length > 1) {
            window.history.back();
          } else {
            window.location.href = '/';
          }
        }
      }
      return;
    }
    
    // 停止心跳检测
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    
    // 设置重连定时器
    if (!this.is_open_socket) {
      this.reconnectTimeOut = setTimeout(() => {
        console.log(`第${this.reconnectNum}次尝试重新连接...`);
        this.connectSocketInit();
      }, 3000); // 3秒后重连
    }
  }

  // 设置消息接收回调
  getMessage(callback) {
    this.messageCallback = callback;
  }

  // 关闭连接
  close() {
    try {
      if (this.socketTask) {
        // 停止心跳检测
        if (this.heartbeatInterval) {
          clearInterval(this.heartbeatInterval);
          this.heartbeatInterval = null;
        }
        
        // 清除重连定时器
        if (this.reconnectTimeOut) {
          clearTimeout(this.reconnectTimeOut);
          this.reconnectTimeOut = null;
        }
        
        // 关闭连接
        this.socketTask.close(1000, "正常关闭");
        this.socketTask = null;
        this.is_open_socket = false;
        console.log("WebSocket连接已主动关闭");
      }
    } catch (error) {
      console.error("关闭WebSocket连接失败:", error);
    }
  }
}

export default websocketUtil;