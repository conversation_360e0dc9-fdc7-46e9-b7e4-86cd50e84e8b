import { weAtob } from './weAtob.js';

/**
 * 音频管理器 - Web端版本
 */
class AudioManager {
  constructor() {
    this.audioContext = null;
    this.audioBuffersMap = new Map(); // 使用Map替代文件系统存储音频数据
    this.tempId = 0; // 用于生成唯一ID

    // 音频播放队列
    this.audioQueue = [];
    // 是否正在播放
    this.isPlaying = false;
    // 是否已经停止
    this.isStopped = false;
    // 存储等待播放完成的回调
    this.playCompletionCallbacks = [];
    // 是否启用超时处理
    this.enableTimeout = true;
    // 超时时间（毫秒），用于检测音频播放是否卡住
    this.timeoutDuration = 60000; // 设置为60秒，适应大多数长音频

    // 关闭调试模式，减少日志输出
    this.debugMode = false;
    
    // 自动播放支持状态
    this.autoplaySupport = {
      checked: false,
      autoplaySupported: false,
      mutedAutoplaySupported: false,
      userInteractionRequired: true
    };
    
    // 初始化Web Audio Context
    this.initAudioContext();
    
    // 尝试解锁音频播放（解决移动端浏览器自动播放限制）
    this.tryUnlockAudio();
    
    // 检测自动播放支持情况
    this.checkAutoplaySupport().then(result => {
      this.autoplaySupport = {
        ...result,
        checked: true
      };
      console.log('自动播放支持状态已更新:', this.autoplaySupport);
    });
  }

  /**
   * 初始化Web Audio Context
   */
  initAudioContext() {
    try {
      // 使用标准Web Audio API
      window.AudioContext = window.AudioContext || window.webkitAudioContext;
      this.audioContext = new AudioContext();
      console.log('Web Audio Context初始化成功');
    } catch (e) {
      console.error('Web Audio Context初始化失败:', e);
    }
  }

  /**
   * 处理音频流数据
   * @param {string} wavStream - Base64编码的WAV音频流
   * @returns {Promise<string>} 临时文件ID
   */
  async handleWavStream(wavStream) {
    try {
      const fileName = `audio_${Date.now()}_${this.tempId++}`;
      console.log('准备处理音频数据:', fileName);

      // 将 Base64 转换为二进制数据
      const binaryString = weAtob(wavStream);
      const rawAudio = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        rawAudio[i] = binaryString.charCodeAt(i);
      }

      // 检查是否已经有WAV头部
      const hasHeader = rawAudio.length > 44 &&
              String.fromCharCode(...rawAudio.slice(0, 4)) === 'RIFF' &&
              String.fromCharCode(...rawAudio.slice(8, 12)) === 'WAVE';

      // 如果没有头部，添加WAV头部
      const audioData = hasHeader ? rawAudio : this.createWavHeader(rawAudio);

      // 存储到Map中
      this.audioBuffersMap.set(fileName, audioData.buffer);
      console.log('音频数据保存成功:', fileName);

      return fileName;
    } catch (error) {
      console.error('处理音频流失败:', error);
      throw error;
    }
  }

  /**
   * 头文件
   * @param {Uint8Array} audioData
   * @param {Number} sampleRate
   * @returns {Uint8Array}
   */
  createWavHeader(audioData, sampleRate = 24000) {
    const numChannels = 1;  // 单声道
    const bitsPerSample = 16;  // 16位
    const byteRate = sampleRate * numChannels * bitsPerSample / 8;
    const blockAlign = numChannels * bitsPerSample / 8;
    const dataSize = audioData.length;
    const headerSize = 44;
    const totalSize = headerSize + dataSize;

    const header = new ArrayBuffer(headerSize);
    const view = new DataView(header);

    // RIFF chunk descriptor
    view.setUint8(0, 'R'.charCodeAt(0));
    view.setUint8(1, 'I'.charCodeAt(0));
    view.setUint8(2, 'F'.charCodeAt(0));
    view.setUint8(3, 'F'.charCodeAt(0));
    view.setUint32(4, totalSize - 8, true);
    view.setUint8(8, 'W'.charCodeAt(0));
    view.setUint8(9, 'A'.charCodeAt(0));
    view.setUint8(10, 'V'.charCodeAt(0));
    view.setUint8(11, 'E'.charCodeAt(0));

    // fmt sub-chunk
    view.setUint8(12, 'f'.charCodeAt(0));
    view.setUint8(13, 'm'.charCodeAt(0));
    view.setUint8(14, 't'.charCodeAt(0));
    view.setUint8(15, ' '.charCodeAt(0));
    view.setUint32(16, 16, true);  // Subchunk1Size (16 for PCM)
    view.setUint16(20, 1, true);   // AudioFormat (1 for PCM)
    view.setUint16(22, numChannels, true);  // NumChannels
    view.setUint32(24, sampleRate, true);   // SampleRate
    view.setUint32(28, byteRate, true);     // ByteRate
    view.setUint16(32, blockAlign, true);   // BlockAlign
    view.setUint16(34, bitsPerSample, true);// BitsPerSample

    // data sub-chunk
    view.setUint8(36, 'd'.charCodeAt(0));
    view.setUint8(37, 'a'.charCodeAt(0));
    view.setUint8(38, 't'.charCodeAt(0));
    view.setUint8(39, 'a'.charCodeAt(0));
    view.setUint32(40, dataSize, true);     // Subchunk2Size

    // 合并头部和音频数据
    const wavArray = new Uint8Array(totalSize);
    wavArray.set(new Uint8Array(header), 0);
    wavArray.set(audioData, headerSize);

    return wavArray;
  }

  /**
   * 获取音频时长
   * @param {string} src - 音频文件ID或URL
   * @returns {Promise<number>} 音频时长（秒）
   */
  getAudioDuration(src) {
    return new Promise((resolve) => {
      try {
        // 如果src为空，直接返回0
        if (!src) {
          console.log('音频路径为空，返回默认时长0');
          resolve(0);
          return;
        }

        // 创建临时音频元素
        const audio = new Audio();
        let timeoutId = null;

        audio.onloadedmetadata = () => {
          if (timeoutId) clearTimeout(timeoutId);
          const duration = parseFloat(audio.duration.toFixed(1));
          console.log('获取到音频时长:', duration);
          resolve(duration);
        };

        audio.onerror = (error) => {
          console.error('音频加载失败:', error);
          if (timeoutId) clearTimeout(timeoutId);
          resolve(0);
        };

        // 检查src是否为ID（从Map获取）或URL
        if (this.audioBuffersMap.has(src)) {
          // 从Map获取音频数据
          const audioBuffer = this.audioBuffersMap.get(src);
          const blob = new Blob([audioBuffer], { type: 'audio/wav' });
          audio.src = URL.createObjectURL(blob);
        } else {
          // 尝试作为URL处理
          audio.src = src;
        }

        // 设置超时处理，防止无限等待
        timeoutId = setTimeout(() => {
          console.log('获取音频时长超时，返回默认值');
          resolve(0);
        }, 3000);
      } catch (error) {
        console.error('获取音频时长过程中出错:', error);
        resolve(0);
      }
    });
  }

  /**
   * 添加音频到播放队列
   * @param {string} audioPath - 音频文件ID或URL
   * @param {number} volume - 音量 0-1
   * @returns {Promise<boolean>} - 添加到队列后的Promise，不等待播放完成
   */
  addToQueue(audioPath, volume = 1) {
    // 验证音频路径
    if (!audioPath) {
      return Promise.resolve(false);
    }

    // 如果已经停止，不添加到队列
    if (this.isStopped) {
      return Promise.resolve(false);
    }

    try {
      // 将音频和回调添加到队列
      this.audioQueue.push({
        path: audioPath,
        volume,
        resolve: () => {}, // 空回调，因为我们不等待播放完成
        reject: () => {}  // 空回调，因为我们不等待播放完成
      });

      // 如果当前没有正在播放的音频且未停止，则开始播放
      if (!this.isPlaying && !this.isStopped) {
        this._processQueue();
      }

      // 立即返回一个已解决的Promise，不等待音频播放完成
      return Promise.resolve(true);
    } catch (error) {
      return Promise.resolve(false);
    }
  }

  /**
   * 处理音频播放队列
   * @private
   */
  _processQueue() {
    // 如果队列为空或已停止，则返回
    if (this.audioQueue.length === 0 || this.isStopped) {
      this.isPlaying = false;

      // 如果队列为空且未停止，通知所有等待播放完成的回调
      if (this.audioQueue.length === 0 && !this.isStopped && this.playCompletionCallbacks.length > 0) {
        // 复制回调数组，然后清空原数组
        const callbacks = [...this.playCompletionCallbacks];
        this.playCompletionCallbacks = [];
        // 触发所有回调
        callbacks.forEach(callback => {
          try {
            callback();
          } catch (e) {
            console.error('触发播放完成回调失败:', e);
          }
        });
      }
      return;
    }

    // 检查队列中的音频是否有效
    if (!this.audioQueue[0] || !this.audioQueue[0].path) {
      this.audioQueue.shift(); // 移除无效音频
      this._processQueue(); // 立即处理下一个
      return;
    }

    // 标记为正在播放
    this.isPlaying = true;

    // 获取队列中的第一个音频
    const audioItem = this.audioQueue.shift();

    // 播放当前音频
    this._playAudio(audioItem.path, audioItem.volume)
      .then(() => {
        // 播放成功，解决Promise
        audioItem.resolve(true);

        // 如果已停止，不继续处理队列
        if (this.isStopped) {
          return;
        }

        // 立即处理下一个音频，不再添加延迟
        this._processQueue();
      })
      .catch((error) => {
        // 播放失败，拒绝Promise
        audioItem.reject(error);

        // 如果已停止，不继续处理队列
        if (this.isStopped) {
          return;
        }

        // 立即处理下一个音频，不再添加延迟
        this._processQueue();
      });
  }

  /**
   * 内部方法：播放单个音频
   * @private
   * @param {string} audioPath - 音频文件ID或URL
   * @param {number} volume - 音量 0-1
   * @returns {Promise<boolean>} - 播放完成后的Promise
   */
  _playAudio(audioPath, volume) {
    return new Promise((resolve, reject) => {
      try {
        // 如果已经停止，直接返回
        if (this.isStopped) {
          resolve(false);
          return;
        }

        const audio = new Audio();
        audio.volume = volume;

        // 检查audioPath是否为ID（从Map获取）或URL
        if (this.audioBuffersMap.has(audioPath)) {
          // 从Map获取音频数据
          const audioBuffer = this.audioBuffersMap.get(audioPath);
          const blob = new Blob([audioBuffer], { type: 'audio/wav' });
          audio.src = URL.createObjectURL(blob);
        } else {
          // 尝试作为URL处理
          audio.src = audioPath;
        }

        let hasResolved = false;
        let timeout;

        const finishPlayback = (success = true, error = null) => {
          if (hasResolved) return; // 防止多次解析
          hasResolved = true;

          // 清除超时定时器
          if (timeout) {
            clearInterval(timeout);
            timeout = null;
          }

          // 释放资源
          URL.revokeObjectURL(audio.src);

          if (success) {
            resolve(true);
          } else {
            reject(error || new Error('音频播放失败'));
          }
        };

        // 错误处理
        audio.onerror = (err) => {
          finishPlayback(false, err);
        };

        // 播放完成
        audio.onended = () => {
          finishPlayback(true);
        };

        // 设置智能超时处理，防止音频卡住
        let lastTime = 0;
        let stuckCounter = 0;

        if (this.enableTimeout) {
          // 使用定时器定期检查音频播放进度
          timeout = setInterval(() => {
            if (!hasResolved && audio) {
              // 检查音频是否在播放
              const currentTime = audio.currentTime;

              // 如果音频播放进度没有变化，可能是卡住了
              if (currentTime > 0 && currentTime === lastTime) {
                stuckCounter++;

                // 如果连续5次检测到播放进度没有变化，认为音频卡住了
                if (stuckCounter >= 5) {
                  clearInterval(timeout);
                  finishPlayback(true); // 即使卡住也算成功，继续下一个
                }
              } else {
                // 如果播放进度有变化，重置卡住计数器
                stuckCounter = 0;
                lastTime = currentTime;
              }
            } else {
              // 如果已经解析或音频不存在，清除定时器
              clearInterval(timeout);
            }
          }, 1000); // 每秒检查一次播放进度
        }

        // 直接播放音频
        audio.play().catch(e => {
          console.error('音频播放失败:', e);
          
          // 检查是否是因为自动播放策略导致的错误
          if (e.name === 'NotAllowedError') {
            console.warn('检测到浏览器自动播放限制，尝试静音播放然后恢复音量');
            
            // 先静音
            const originalVolume = audio.volume;
            audio.volume = 0;
            
            // 尝试静音播放
            audio.play().then(() => {
              console.log('静音播放成功，尝试逐渐恢复音量');
              
              // 逐渐恢复音量，避免突然的声音
              let currentVolume = 0;
              const volumeInterval = setInterval(() => {
                currentVolume += 0.1;
                if (currentVolume >= originalVolume) {
                  currentVolume = originalVolume;
                  clearInterval(volumeInterval);
                }
                audio.volume = currentVolume;
              }, 200);
              
            }).catch(err => {
              console.error('即使静音也无法播放，显示交互按钮:', err);
              
              // 如果静音播放也失败，显示交互按钮
              const unlockButton = document.createElement('button');
              unlockButton.innerText = '点击启用音频播放';
              unlockButton.style.position = 'fixed';
              unlockButton.style.zIndex = '9999';
              unlockButton.style.top = '50%';
              unlockButton.style.left = '50%';
              unlockButton.style.transform = 'translate(-50%, -50%)';
              unlockButton.style.padding = '10px 20px';
              unlockButton.style.backgroundColor = '#4CAF50';
              unlockButton.style.color = 'white';
              unlockButton.style.border = 'none';
              unlockButton.style.borderRadius = '5px';
              unlockButton.style.cursor = 'pointer';
              
              // 当用户点击按钮时，尝试再次播放并移除按钮
              unlockButton.onclick = () => {
                document.body.removeChild(unlockButton);
                audio.volume = originalVolume; // 恢复原始音量
                audio.play().then(() => {
                  console.log('用户交互后音频播放成功');
                }).catch(err => {
                  console.error('即使在用户交互后，音频播放仍然失败:', err);
                  finishPlayback(false, err);
                });
              };
              
              document.body.appendChild(unlockButton);
            });
          } else {
            finishPlayback(false, e);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 直接播放音频（不使用队列）
   * @param {string} audioPath - 音频文件ID或URL
   * @param {number} volume - 音量 0-1
   * @returns {Promise<boolean>} - 播放完成后的Promise
   */
  async playDirectly(audioPath, volume = 1) {
    console.log('直接播放音频:', audioPath);
    return this._playAudio(audioPath, volume);
  }

  /**
   * 播放音频（兼容旧版API）
   * @param {string} audioPath - 音频文件ID或URL
   * @param {number} volume - 音量 0-1
   * @returns {Promise<boolean>} - 播放完成后的Promise
   */
  async play(audioPath, volume = 1) {
    console.log('使用队列播放音频:', audioPath);
    return this.addToQueue(audioPath, volume);
  }

  /**
   * 等待所有音频播放完成
   * @returns {Promise<void>} - 播放完成后的Promise
   */
  waitForPlaybackComplete() {
    // 如果没有正在播放的音频，直接返回已解决的Promise
    if (!this.isPlaying && this.audioQueue.length === 0) {
      console.log('没有正在播放的音频，直接返回');
      return Promise.resolve();
    }

    // 否则返回一个新的Promise，当所有音频播放完成时解决
    return new Promise(resolve => {
      console.log('添加播放完成回调');
      this.playCompletionCallbacks.push(resolve);
    });
  }

  /**
   * 停止播放并清空队列
   * @returns {Promise<void>} - 当状态完全重置后解决的Promise
   */
  stop() {
    // 立即标记为停止状态，防止新的音频被处理
    this.isStopped = true;

    // 清空队列，并解决所有等待中的Promise
    const queue = this.audioQueue;
    this.audioQueue = [];

    // 解决所有等待中的Promise
    queue.forEach(item => {
      try {
        item.resolve(false); // 使用false表示被中断
      } catch (e) {
        // 忽略错误
      }
    });

    // 触发所有等待播放完成的回调
    if (this.playCompletionCallbacks.length > 0) {
      const callbacks = [...this.playCompletionCallbacks];
      this.playCompletionCallbacks = [];
      callbacks.forEach(callback => {
        try {
          callback();
        } catch (e) {
          // 忽略错误
        }
      });
    }

    // 重置播放状态
    this.isPlaying = false;

    // 返回一个Promise，当状态完全重置后解决
    return new Promise(resolve => {
      // 减少延迟时间，加快重置速度
      setTimeout(() => {
        this.isStopped = false;
        resolve();
      }, 100);
    });
  }

  /**
   * 清理临时音频数据
   */
  clearTempFiles() {
    try {
      this.audioBuffersMap.clear();
      console.log('清理音频数据成功');
    } catch (error) {
      console.error('清理音频数据失败:', error);
    }
  }

  /**
   * 设置是否启用超时处理
   * @param {boolean} enable - 是否启用
   */
  setTimeoutEnabled(enable) {
    this.enableTimeout = !!enable;
    console.log(`超时处理已${this.enableTimeout ? '启用' : '禁用'}`);
  }

  /**
   * 设置超时时间
   * @param {number} duration - 超时时间（毫秒）
   */
  setTimeoutDuration(duration) {
    if (typeof duration === 'number' && duration > 0) {
      this.timeoutDuration = duration;
      console.log(`超时时间已设置为 ${this.timeoutDuration} 毫秒`);
    } else {
      console.warn('超时时间必须是正数');
    }
  }

  /**
   * 销毁实例
   */
  destroy() {
    this.stop();
    this.clearTempFiles();
    if (this.audioContext) {
      try {
        // 在较新的浏览器中，需要先关闭AudioContext
        if (this.audioContext.state !== 'closed') {
          this.audioContext.close();
        }
      } catch (e) {
        console.error('关闭AudioContext失败:', e);
      }
      this.audioContext = null;
    }
  }

  /**
   * 尝试解锁音频播放（在页面加载时调用）
   * 在移动端浏览器中，音频播放需要用户交互才能启用
   * 这个方法会在用户首次点击页面时解锁音频播放功能
   */
  tryUnlockAudio() {
    // 创建一个空白的音频元素
    const silentAudio = new Audio();
    silentAudio.src = "data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA";
    silentAudio.volume = 0.01; // 设置极低的音量
    
    // 用户交互事件列表
    const userInteractionEvents = [
      'touchstart', 'touchend', 'mousedown', 'keydown', 'click'
    ];
    
    // 解锁音频的函数
    const unlockAudio = () => {
      console.log('用户交互，尝试解锁音频播放');
      
      // 尝试播放静音音频
      silentAudio.play().then(() => {
        console.log('音频播放已解锁');
        
        // 成功后，移除所有事件监听器
        userInteractionEvents.forEach(event => {
          document.removeEventListener(event, unlockAudio);
        });
      }).catch(err => {
        console.warn('尝试解锁音频失败:', err);
      });
      
      // 如果AudioContext处于暂停状态，尝试恢复
      if (this.audioContext && this.audioContext.state === 'suspended') {
        this.audioContext.resume().then(() => {
          console.log('AudioContext已恢复');
        }).catch(err => {
          console.warn('恢复AudioContext失败:', err);
        });
      }
    };
    
    // 为所有用户交互事件添加监听器
    userInteractionEvents.forEach(event => {
      document.addEventListener(event, unlockAudio, { once: false });
    });
    
    console.log('已设置音频解锁监听器，等待用户交互');
  }

  /**
   * 检测当前浏览器环境是否支持自动播放音频
   * @returns {Promise<Object>} 返回包含支持信息的对象
   */
  async checkAutoplaySupport() {
    const result = {
      autoplaySupported: false,
      mutedAutoplaySupported: false,
      userInteractionRequired: true,
      reason: ''
    };
    
    try {
      // 创建测试音频元素
      const audio = new Audio();
      audio.src = "data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA";
      
      // 测试有声自动播放
      try {
        await audio.play();
        result.autoplaySupported = true;
        result.userInteractionRequired = false;
        result.reason = '支持完全自动播放';
      } catch (e) {
        if (e.name === 'NotAllowedError') {
          result.reason = '需要用户交互才能播放有声音频';
          
          // 测试静音自动播放
          try {
            audio.muted = true;
            await audio.play();
            result.mutedAutoplaySupported = true;
            result.reason = '仅支持静音自动播放，需要用户交互才能播放有声音频';
          } catch (e2) {
            result.reason = '即使静音也不支持自动播放，需要用户交互';
          }
        } else {
          result.reason = `自动播放测试出错: ${e.message}`;
        }
      }
    } catch (e) {
      result.reason = `测试过程发生错误: ${e.message}`;
    }
    
    console.log('自动播放支持检测结果:', result);
    return result;
  }
}

// 导出单例
export const audioManager = new AudioManager();
