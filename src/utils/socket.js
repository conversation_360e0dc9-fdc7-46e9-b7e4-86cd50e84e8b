/**
 * websocket
 */
let data = {}
let websocket = null; // 用于存储实例化后websocket
let isConnect = false; // 连接标识 避免重复连接
let rec; // 断线重连后，延迟5秒重新创建WebSocket连接  rec用来存储延迟请求的代码
let heartbeatTime  // 心d定时
let global_callback = null;
let reConnectNum = 0; // 重连时间
import { MessageBox, Message } from 'element-ui';
// 创建websocket
function createWebSocket(e, callback) {
  data = e;
  global_callback = callback
  // 判断当前浏览器是否支持WebSocket
  if ("WebSocket" in window) {
    console.log("当前浏览器 windows");
  } else if ("MozWebSocket" in window) {
    console.log("当前浏览器 windows");
  } else {
    console.log("当前浏览器 Not support websocket");
  }

  try {
    initWebSocket(e); // 初始化websocket连接
  } catch (e) {
    console.log("尝试创建连接失败");
    reConnect(); // 如果无法连接上 webSocket 那么重新连接！可能会因为服务器重新部署，或者短暂断网等导致无法创建连接
  }
}

// 初始化websocket
function initWebSocket(e) {
  console.log(e);

  websocket = new WebSocket(e.url);
  // console.log("websocket:", websocket);

  websocket.onopen = function (e) {
    websocketOpen(e);
  };

  // 接收
  websocket.onmessage = function (e) {
    websocketOnMessage(e);
  };

  // 连接发生错误
  websocket.onerror = function () {
    console.log("WebSocket连接发生错误");
    isConnect = false; // 连接断开修改标识
    reConnect(); // 连接错误 需要重连
  };

  websocket.onclose = function (e) {
    websocketClose(e);
  };
}

// 定义重连函数
let reConnect = () => {
  reConnectNum++;
  if (reConnectNum > 3) {
    // console.log("重连次数过多，停止重连");
    MessageBox.confirm('连接对话服务失败,是否继续尝试重连?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      reConnectNum = 0
      reConnect()
    }).catch(() => {
      Message.info('取消重连');
    });
    return;
  }
  console.log("尝试重新连接");
  if (isConnect) return; // 如果已经连上就不在重连了
  clearInterval(heartbeatTime);
  rec && clearTimeout(rec);
  rec = setTimeout(function () {

    // 延迟5秒重连  避免过多次过频繁请求重连
    createWebSocket(data, global_callback);
  }, 5000);
};

// 创建连接
function websocketOpen(e) {
  console.log("连接成功", e);
  sendHeartbeat()
}

// 发送心跳
function sendHeartbeat() {
  heartbeatTime = setInterval(() => {
    sendWebSocket({
      type: 'ping'
    });
  }, 5000);
}


// 数据接收
function websocketOnMessage(e) {
  if (!e.data.includes('pong')) {
    global_callback(e.data)
  }
  // console.log("数据接收", e.data);
  // let data = JSON.parse(decodeUnicode(e.data))
}
// 关闭
function websocketClose(e) {
  isConnect = false; // 断开后修改标识
  clearInterval(heartbeatTime);
  console.log("connection closed (" + e.code + ")");
  if( e.code === 1006 ) {
    reConnect(); // 断线重连
  }
}

// 数据发送
function websocketSend(data) {
  // console.log("发送的数据", data, JSON.stringify(data));
  websocket.send(JSON.stringify(data));
}

// 实际调用的方法==============

// 发送
function sendWebSocket(data) {
  if (websocket.readyState === websocket.OPEN) { // 开启状态
    websocketSend(data);
  } else { // 若 未开启 / 正在开启 状态 ，则等待1s后重新调用
    setTimeout(function () {
      sendWebSocket(data);
    }, 1000);
  }
}

// 关闭
let closeWebSocket = () => {
  if (websocket == null) return;
  websocket.close();
  console.log('关闭成功');

  clearInterval(heartbeatTime);
};

export {
  sendWebSocket,
  createWebSocket,
  closeWebSocket,
};

