import axios from "axios"
import { Message} from 'element-ui'

// 调用axios.create方法，配置一些属性，返回一个新的axios
const request= axios.create({
    baseURL: process.env.VUE_APP_BASE_API,
    //请求超时时间
    timeout: 60000
})

// 请求拦截
request.interceptors.request.use(
	//config 代表是你请求的一些信息
    config => {
        // if (config.method == 'post' || config.method == 'put') {
        //     config.data = qs.stringify(config.data)
        // }
        // console.log(config);
        // 在请求发送之前的操作
       
        if (window.localStorage.getItem('token')) {
            let token=window.localStorage.getItem('token')
            config.headers['Authorization']= token
            config.headers['VERSION']= '1.5.1-saas'
        }
        
        // config.headers['Content-Type'] = 'application/json'
        return config
    },
    error => {
        return Promise.reject(error)
    }
)

//  response拦截器 响应拦截器 请求之后的操作
request.interceptors.response.use(
    config => {
        if (config.data.code == 1 ) {
            Message({
                showClose: true,
                message: config.data.msg,
                type: "error"
            })
            return
        }
        
        // console.log(config.data);
        return config.data
    },
    error => {
        if (error) {
            Message({
                showClose: true,
                message: error.message,
                type: "error"
            })
            
        }
        return Promise.reject(error)
    }
)

export default request;