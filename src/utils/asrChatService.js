/**
 * 语音识别和文本转语音服务 - Web端版本
 * 封装了WebSocket和HTTP请求的逻辑，用于处理语音识别和文本转语音功能
 */
import { audioManager } from './audioManager';
import websocketUtil from './websocket';

export class ChatService {
  constructor() {
    // WebSocket相关
    this.chatSocket = null;
    this.isChatConnected = false;

    // 回调函数
    this.onMessage = null; // 消息回调
    this.onError = null; // 错误回调
  }

  /**
   * 初始化聊天服务
   * @param {Object} config - 配置对象
   * @param {string} config.wsUrl - WebSocket URL
   * @param {Object} config.headers - WebSocket头信息，必须包含以下参数：
   * @param {string} config.headers.Voice - 语音类型，如 'zf_xiaobei', 'zf_xiaoni', 'zf_xiaoxiao' 等
   * @param {string} config.headers.AuthToken - 认证令牌
   * @param {string} config.headers.Mode - 模式，如 'KnowledgeBase'
   * @param {string} config.headers.DeviceType - 设备类型，如 'sci_fitness'
   * @param {Function} config.onMessage - 消息回调
   * @param {Function} config.onError - 错误回调
   * @returns {Promise<boolean>} - 初始化是否成功
   */
  async init(config) {
    try {
      // 检查配置对象是否存在
      if (!config) {
        console.error('初始化失败：配置对象不能为空');
        return false;
      }

      // 设置回调函数
      this.onMessage = config.onMessage;
      this.onError = config.onError || ((error) => console.error('聊天服务错误:', error));

      // 检查wsUrl是否存在
      if (!config.wsUrl) {
        console.error('初始化失败：wsUrl不能为空');
        this.onError(new Error('初始化失败：wsUrl不能为空'));
        return false;
      }

      // 检查headers是否存在
      if (!config.headers) {
        console.error('初始化失败：headers不能为空');
        this.onError(new Error('初始化失败：headers不能为空'));
        return false;
      }

      // 检查必要的headers参数
      const requiredHeaders = ['AuthToken', 'Voice', 'Mode', 'DeviceType'];
      for (const header of requiredHeaders) {
        if (!config.headers[header]) {
          console.error(`初始化失败：headers中缺少必要参数 ${header}`);
          this.onError(new Error(`初始化失败：headers中缺少必要参数 ${header}`));
          return false;
        }
      }

      // 初始化WebSocket连接
      return this.initWebSocket(config.wsUrl, config.headers);
    } catch (error) {
      if (this.onError) {
        this.onError(error);
      } else {
        console.error('聊天服务错误:', error);
      }
      return false;
    }
  }

  /**
   * 初始化WebSocket连接
   * @param {string} wsUrl - WebSocket URL
   * @param {Object} headers - WebSocket头信息
   * @returns {Promise<boolean>} - 初始化是否成功
   */
  initWebSocket(wsUrl, headers) {
    return new Promise((resolve) => {
      try {
        console.log('初始化聊天WebSocket连接');
        console.log('WebSocket URL:', wsUrl);
        console.log('WebSocket头信息:', headers);

        // 创建WebSocket连接
        this.chatSocket = new websocketUtil(wsUrl, 8, headers);
        console.log('聊天WebSocket对象已创建:', !!this.chatSocket);

        // WebSocket连接建立后会自动设置is_open_socket为true
        this.isChatConnected = true;
        console.log('isChatConnected已设置为true');

        // 设置消息处理函数
        this.chatSocket.getMessage((res) => {
          console.log('收到聊天WebSocket消息:', res);

          try {
            // 检查res和res.data是否存在
            if (!res || res.data === undefined) {
              console.error('收到无效的WebSocket消息:', res);
              return;
            }

            // 记录原始消息数据，便于调试
            // console.log('收到WebSocket消息原始数据:', res.data);

            // 尝试解析JSON数据
            let data;
            try {
              // 检查res.data是否为字符串且不为空
              if (typeof res.data === 'string' && res.data.trim() !== '') {
                data = JSON.parse(res.data);
              } else if (typeof res.data === 'object') {
                // 如果已经是对象，直接使用
                data = res.data;
              } else {
                console.error('无法解析的WebSocket消息数据类型:', typeof res.data);
                return;
              }
            } catch (parseError) {
              console.error('解析WebSocket消息失败:', parseError, '原始数据:', res.data);
              this.onError(parseError);
              return;
            }

            // 检查解析后的数据是否有效
            if (!data) {
              console.error('解析后的WebSocket消息数据为空');
              return;
            }

            console.log('解析后的WebSocket消息数据:', data);

            // 调用消息回调
            if (this.onMessage) {
              this.onMessage(res);
            }
          } catch (e) {
            console.error('处理聊天WebSocket消息失败:', e);
            this.onError(e);
          }
        });

        // 添加错误处理
        if (this.chatSocket.socketTask) {
          this.chatSocket.socketTask.onerror = (error) => {
            console.error('聊天WebSocket错误:', error);
            this.onError(error);
          };

          // 添加关闭处理
          this.chatSocket.socketTask.onclose = (event) => {
            console.log('聊天WebSocket关闭:', event);
            this.isChatConnected = false;
          };
        }

        resolve(true);
      } catch (error) {
        console.error('聊天WebSocket连接失败:', error);
        this.isChatConnected = false;
        this.onError(error);
        resolve(false);
      }
    });
  }

  /**
   * 发送消息
   * @param {Object} message - 消息对象
   * @returns {Promise<boolean>} - 发送是否成功
   */
  async send(message) {
    return new Promise((resolve) => {
      try {
        if (!this.chatSocket) {
          console.error('聊天WebSocket未连接');
          resolve(false);
          return;
        }

        this.chatSocket.send(message);
        console.log('聊天消息发送成功:', message);
        resolve(true);
      } catch (error) {
        console.error('发送聊天消息失败:', error);
        this.onError(error);
        resolve(false);
      }
    });
  }

  /**
   * 关闭连接
   */
  close() {
    if (this.chatSocket) {
      this.chatSocket.close();
      this.chatSocket = null;
      this.isChatConnected = false;
      console.log('聊天WebSocket已关闭');
    }
  }
}

export class AsrChatService {
  constructor() {
    // WebSocket相关
    this.asrSocket = null;
    this.isAsrConnected = false;

    // 录音相关
    this.isRecording = false;
    this.isSpeaking = false;
    this.silenceCounter = 0;
    this.audioBuffer = [];
    this.lastAudioBuffer = null;
    this.frameCounter = 0;
    this.recorderManager = null;
    this.mediaRecorder = null;
    this.mediaStream = null;

    // 模式设置
    this.asrMode = 'websocket'; // 'http' 或 'websocket'

    // URL设置
    this.wsUrl = ''; // WebSocket URL
    this.httpUrl = ''; // HTTP URL

    // 回调函数
    this.onUserSpeechRecognized = null; // 用户语音识别结果回调
    this.onBotResponse = null; // 机器人回答回调
    this.onFinish = null; // 对话结束回调
    this.onError = null; // 错误回调
  }

  /**
   * 初始化语音识别和文本转语音服务
   * @param {Object} config - 配置对象
   * @param {string} config.wsUrl - WebSocket URL
   * @param {string} config.httpUrl - HTTP URL
   * @param {Object} config.headers - WebSocket头信息，必须包含以下参数：
   * @param {string} config.headers.AuthToken - 认证令牌
   * @param {string} config.headers.Voice - 语音类型，如 'zf_xiaobei', 'zf_xiaoni', 'zf_xiaoxiao' 等
   * @param {string} config.headers.Mode - 模式，如 'KnowledgeBase'
   * @param {string} config.headers.DeviceType - 设备类型，如 'sci_fitness'
   * @param {string} config.headers.AgentId - 智能体ID
   * @param {string} config.headers.UserId - 用户ID
   * @param {string} config.headers.ConversationId - 会话ID
   * @param {string} config.headers.SampleRate - 采样率，默认 '16000'
   * @param {string} config.headers.StartValue - 开始值，默认 '4000'
   * @param {string} config.headers.EndValue - 结束值，默认 '3000'
   * @param {string} config.headers.ChunkSize - 块大小，默认 '1024'
   * @param {string} config.headers.Role - 角色，默认 'BotA'
   * @param {Function} config.onUserSpeechRecognized - 用户语音识别结果回调
   * @param {Function} config.onBotResponse - 机器人回答回调
   * @param {Function} config.onFinish - 对话结束回调
   * @param {Function} config.onError - 错误回调
   * @param {string} config.asrMode - 音频转文字模式：'http' 或 'websocket'
   * @returns {Promise<boolean>} - 初始化是否成功
   */
  async init(config) {
    try {
      // 检查配置对象是否存在
      if (!config) {
        console.error('初始化失败：配置对象不能为空');
        return false;
      }

      // 设置回调函数
      this.onUserSpeechRecognized = config.onUserSpeechRecognized;
      this.onBotResponse = config.onBotResponse;
      this.onFinish = config.onFinish;
      this.onError = config.onError || ((error) => console.error('ASR服务错误:', error));

      // 设置模式
      this.asrMode = config.asrMode || 'websocket';

      // 保存URL
      this.wsUrl = config.wsUrl || '';
      this.httpUrl = config.httpUrl || '';

      // 检查URL是否有效
      if (this.asrMode === 'websocket' && !this.wsUrl) {
        console.error('WebSocket模式下必须提供wsUrl');
        this.onError(new Error('WebSocket模式下必须提供wsUrl'));
        return false;
      }

      if (this.asrMode === 'http' && !this.httpUrl) {
        console.error('HTTP模式下必须提供httpUrl');
        this.onError(new Error('HTTP模式下必须提供httpUrl'));
        return false;
      }

      // 检查headers是否存在
      if (!config.headers) {
        console.error('初始化失败：headers不能为空');
        this.onError(new Error('初始化失败：headers不能为空'));
        return false;
      }

      // 检查必要的headers参数
      const requiredHeaders = ['AuthToken', 'Voice', 'Mode', 'DeviceType'];
      for (const header of requiredHeaders) {
        if (!config.headers[header]) {
          console.error(`初始化失败：headers中缺少必要参数 ${header}`);
          this.onError(new Error(`初始化失败：headers中缺少必要参数 ${header}`));
          return false;
        }
      }

      // 设置默认值
      const headers = { ...config.headers };
      headers.SampleRate = headers.SampleRate || '16000';
      headers.StartValue = headers.StartValue || '4000';
      headers.EndValue = headers.EndValue || '3000';
      headers.ChunkSize = headers.ChunkSize || '1024';
      headers.Role = headers.Role || 'BotA';

      // 初始化WebSocket连接
      return this.initWebSocket(config.wsUrl, headers);
    } catch (error) {
      if (this.onError) {
        this.onError(error);
      } else {
        console.error('ASR服务错误:', error);
      }
      return false;
    }
  }

  /**
   * 初始化WebSocket连接
   * @param {string} wsUrl - WebSocket URL
   * @param {Object} headers - WebSocket头信息
   * @returns {Promise<boolean>} - 初始化是否成功
   */
  initWebSocket(wsUrl, headers) {
    return new Promise((resolve) => {
      try {
        console.log('初始化 ASR WebSocket 连接');
        console.log('WebSocket URL:', wsUrl);
        console.log('WebSocket 头信息:', headers);

        // 创建 WebSocket 连接
        this.asrSocket = new websocketUtil(wsUrl, 8, headers);
        console.log('ASR WebSocket 对象已创建:', !!this.asrSocket);

        // WebSocket 连接建立后会自动设置 is_open_socket 为 true
        this.isAsrConnected = true;
        console.log('isAsrConnected 已设置为 true');

        // 设置消息处理函数
        this.asrSocket.getMessage(this._handleWebSocketMessage.bind(this));
        console.log('ASR WebSocket 消息处理函数已设置');

        // 添加错误处理
        if (this.asrSocket.socketTask) {
          this.asrSocket.socketTask.onerror = (error) => {
            console.error('ASR WebSocket 错误:', error);
            this.onError(error);
          };

          // 添加关闭处理
          this.asrSocket.socketTask.onclose = (event) => {
            console.log('ASR WebSocket 关闭:', event);
            this.isAsrConnected = false;
          };
        }

        resolve(true);
      } catch (error) {
        console.error('ASR WebSocket 连接失败:', error);
        this.isAsrConnected = false;
        this.onError(error);
        resolve(false);
      }
    });
  }

  /**
   * 处理WebSocket消息
   * @param {Object} res - WebSocket消息
   * @private
   */
  _handleWebSocketMessage(res) {
    console.log('收到 ASR WebSocket 消息:', res);

    try {
      // 检查res和res.data是否存在
      if (!res || res.data === undefined) {
        console.error('收到无效的ASR WebSocket消息:', res);
        return;
      }

      // 记录原始消息数据，便于调试
      // console.log('收到ASR WebSocket消息原始数据:', res.data);

      // 尝试解析JSON数据
      let data;
      try {
        // 检查res.data是否为字符串且不为空
        if (typeof res.data === 'string' && res.data.trim() !== '') {
          data = JSON.parse(res.data);
          console.log('解析后的 JSON 数据:', data);
        } else if (typeof res.data === 'object') {
          // 如果已经是对象，直接使用
          data = res.data;
          console.log('非字符串数据(对象):', data);
        } else {
          console.error('无法解析的ASR WebSocket消息数据类型:', typeof res.data);
          return;
        }
      } catch (parseError) {
        console.error('解析ASR WebSocket消息失败:', parseError, '原始数据:', res.data);
        this.onError(parseError);
        return;
      }

      // 检查解析后的数据是否有效
      if (!data) {
        console.error('解析后的ASR WebSocket消息数据为空');
        return;
      }

      // 详细记录收到的数据
      console.log('ASR 消息详情:', {
        data_type: typeof res.data,
        data_content: res.data,
        parsed_data: data
      });

      // 检查data.content是否存在
      if (data && data.content !== undefined) {
        console.log('ASR 识别结果:', data.content);

        // 检查是否是结束消息
        if (data.content === '<Finish>') {
          console.log('收到 <Finish> 消息，标记对话结束');

          // 调用对话结束回调
          if (this.onFinish) {
            this.onFinish();
          }
        } else if (data.wav_stream) {
          // 有wav_stream，是机器人回答
          console.log('这是机器人的回答，包含音频');

          // 调用机器人回答回调
          if (this.onBotResponse) {
            this.onBotResponse(data);
          }
        } else {
          // 没有wav_stream，是用户说的内容
          console.log('这是用户的语音输入，不包含音频');

          // 调用用户语音识别结果回调
          if (this.onUserSpeechRecognized && typeof data.content === 'string') {
            this.onUserSpeechRecognized(data.content);
          }
        }
      } else {
        console.log('ASR 消息不包含 content 字段或content为undefined');

        // 即使没有content字段，如果有wav_stream，也可能是机器人回答
        if (data && data.wav_stream) {
          console.log('这是机器人的回答，包含音频但没有content字段');

          // 调用机器人回答回调，确保data.content存在
          if (this.onBotResponse) {
            data.content = data.content || '';
            this.onBotResponse(data);
          }
        }
      }
    } catch (e) {
      console.error('处理ASR WebSocket消息失败:', e, '原始消息:', res);
      this.onError(e);
    }
  }

  /**
   * 开始录音
   * @returns {Promise<boolean>} - 开始录音是否成功
   */
  startRecording() {
    return new Promise(async (resolve) => {
      console.log('开始录音');

      // 检查 WebSocket 是否已连接
      if (!this.isAsrConnected) {
        console.log('ASR WebSocket 未连接，无法开始录音');
        this.onError(new Error('语音识别服务未连接'));
        resolve(false);
        return;
      }

      try {
        // 检查浏览器是否支持MediaRecorder API
        if (!navigator.mediaDevices || !window.MediaRecorder) {
          console.error('浏览器不支持MediaRecorder API');
          this.onError(new Error('浏览器不支持录音功能'));
          resolve(false);
          return;
        }

        // 请求麦克风权限
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        this.mediaStream = stream;

        // 设置录音状态和重置缓冲区
        this.isRecording = true;
        this.isSpeaking = false;
        this.silenceCounter = 0;
        this.audioBuffer = []; // 清空音频缓冲区

        console.log('录音状态已设置 - isRecording:', this.isRecording, 'isAsrConnected:', this.isAsrConnected);

        // 创建音频处理上下文，用于分析音量
        const audioContext = new AudioContext({ sampleRate: 16000 });
        const source = audioContext.createMediaStreamSource(stream);
        const analyser = audioContext.createAnalyser();
        analyser.fftSize = 2048;
        source.connect(analyser);

        // 创建处理器节点，用于处理音频数据
        const scriptNode = audioContext.createScriptProcessor(1024, 1, 1);
        scriptNode.onaudioprocess = (audioProcessingEvent) => {
          if (!this.isRecording) return;

          // 获取音频输入数据
          const inputBuffer = audioProcessingEvent.inputBuffer;
          const inputData = inputBuffer.getChannelData(0);

          // 计算音量
          let sum = 0;
          for (let i = 0; i < inputData.length; i++) {
            sum += inputData[i] * inputData[i];
          }
          const volume = Math.sqrt(sum / inputData.length) * 1000;

          // 处理音频数据
          this._handleAudioFrame(inputData, volume);
        };

        // 连接节点
        analyser.connect(scriptNode);
        scriptNode.connect(audioContext.destination);

        // 创建MediaRecorder
        this.mediaRecorder = new MediaRecorder(stream);

        // 设置数据可用事件
        this.mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0 && this.asrMode === 'http') {
            // HTTP模式：累积数据
            this.audioBuffer.push(event.data);
          }
        };

        // 录音停止事件
        this.mediaRecorder.onstop = () => {
          // 如果有累积的音频数据，根据当前模式发送它们
          if (this.audioBuffer && this.audioBuffer.length > 0 && this.asrMode === 'http') {
            console.log('录音停止，HTTP模式：发送累积的音频数据');
            this.sendAccumulatedAudioViaHttp();
          }

          // 重置录音和说话状态
          this.isRecording = false;
          this.isSpeaking = false;
          this.frameCounter = 0;

          // 清理资源
          source.disconnect();
          analyser.disconnect();
          scriptNode.disconnect();
          audioContext.close();

          // 停止流
          if (this.mediaStream) {
            this.mediaStream.getTracks().forEach(track => track.stop());
            this.mediaStream = null;
          }
        };

        // 开始录音
        this.mediaRecorder.start(100); // 每100毫秒生成一个数据片段
        console.log('录音已开始');

        resolve(true);
      } catch (error) {
        console.error('开始录音失败:', error);
        this.isRecording = false;
        this.onError(error);
        resolve(false);
      }
    });
  }

  /**
   * 停止录音
   */
  stopRecording() {
    if (this.isRecording && this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
      this.isRecording = false;
      console.log('录音已停止');
    }
  }

  /**
   * 处理音频帧数据
   * @param {Float32Array} audioData - 音频帧数据
   * @param {number} volume - 音频音量
   * @private
   */
  _handleAudioFrame(audioData, volume) {
    // 确保在处理音频帧时，录音状态和WebSocket连接都是正常的
    if (!this.isRecording || !this.isAsrConnected) {
      return;
    }

    try {
      // 将 Float32Array 转换为 Int16Array 用于发送
      const pcmData = this.convertFloat32ToInt16(audioData);
      if (!pcmData || pcmData.length === 0) return;

      this.lastAudioBuffer = pcmData;

      // 只在检测到说话时才处理音频数据
      if (volume > 50) { // 阈值为50
        // 更新说话状态
        if (!this.isSpeaking) {
          this.isSpeaking = true;
          this.frameCounter = 0; // 重置帧计数器
          // 保留开始说话的日志，这是重要的状态变化
          console.log('检测到开始说话');
        }
        this.silenceCounter = 0;

        // WebSocket模式：每次采集到用户说话的音频就发送
        if (this.asrMode === 'websocket') {
          this.frameCounter++;
          this.sendAudioFrameViaWebSocket(pcmData);
        }
      } else if (this.isSpeaking) {
        // 之前在说话，现在音量低
        this.silenceCounter++;

        // 在静音期间仍然收集一段时间的音频数据，以捕获句子的结尾
        if (this.silenceCounter <= 10) {
          if (this.asrMode === 'websocket') {
            // WebSocket模式：继续发送帧数据
            this.frameCounter++;
            this.sendAudioFrameViaWebSocket(pcmData);
          }
        } else {
          // 连续10帧静音，认为说话结束
          this.isSpeaking = false;
          // 保留停止说话的日志，这是重要的状态变化
          console.log('检测到停止说话');
        }
      }
    } catch (error) {
      console.error('处理音频数据失败:', error);
      this.onError(error);
    }
  }

  /**
   * 通过WebSocket发送单帧音频数据（WebSocket模式专用）
   * @param {Int16Array} audioData - 音频数据
   */
  sendAudioFrameViaWebSocket(audioData) {
    if (!audioData || audioData.length === 0) return;

    try {
      // 检查WebSocket连接是否可用
      if (!this.asrSocket || !this.asrSocket.socketTask) {
        console.error('WebSocket连接不可用');
        return;
      }

      // 发送二进制数据
      this.asrSocket.socketTask.send(audioData.buffer);
      console.log('WebSocket模式：音频帧发送成功');
    } catch (error) {
      console.error('WebSocket模式：发送音频帧失败:', error);
      this.onError(error);
    }
  }

  /**
   * 通过HTTP发送累积的音频数据
   */
  sendAccumulatedAudioViaHttp() {
    if (!this.audioBuffer || this.audioBuffer.length === 0) return;

    try {
      // 合并所有Blob数据
      const blob = new Blob(this.audioBuffer, { type: 'audio/wav' });

      // 简化日志输出
      console.log(`HTTP模式：准备发送音频数据，大小: ${blob.size} 字节`);

      // 创建必要的头信息
      const headers = {
        'start_value': '60',
        'end_value': '50',
        'chunk_size': '1024',
        'sample_rate': '16000',
        'Content-Type': 'application/octet-stream',
        'AuthToken': localStorage.getItem('token')
      };

      // 检查HTTP URL是否有效
      if (!this.httpUrl) {
        console.error('HTTP URL未设置，无法发送请求');
        this.onError(new Error('HTTP URL未设置，无法发送请求'));
        return;
      }

      console.log(`HTTP请求URL: ${this.httpUrl}`);

      // 使用Fetch API发送HTTP请求
      fetch(this.httpUrl, {
        method: 'POST',
        headers: headers,
        body: blob
      })
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status}`);
          }
          return response.json();
        })
        .then(data => {
          console.log(`HTTP请求成功，服务器响应:`, data);

          // 如果服务器返回了识别结果，调用回调函数
          if (data && data.content) {
            if (this.onUserSpeechRecognized) {
              this.onUserSpeechRecognized(data.content);
            }
          }
        })
        .catch(error => {
          console.error(`HTTP请求失败:`, error);
          this.onError(error);
        });

      // 清空缓冲区
      this.audioBuffer = [];
    } catch (error) {
      console.error('通过HTTP发送累积音频数据失败:', error);
      this.onError(error);
      // 清空缓冲区，防止错误数据累积
      this.audioBuffer = [];
    }
  }

  /**
   * 将 Float32Array 转换为 Int16Array
   * @param {Float32Array} float32Array - 浮点数音频数据
   * @returns {Int16Array} - 整数音频数据
   */
  convertFloat32ToInt16(float32Array) {
    const int16Array = new Int16Array(float32Array.length);
    for (let i = 0; i < float32Array.length; i++) {
      const s = Math.max(-1, Math.min(1, float32Array[i]));
      int16Array[i] = s < 0 ? s * 0x8000 : s * 0x7fff;
    }
    return int16Array;
  }

  /**
   * 关闭连接并清理资源
   */
  close() {
    try {
      // 停止录音
      this.stopRecording();

      // 关闭WebSocket连接
      if (this.asrSocket) {
        this.asrSocket.close();
        this.asrSocket = null;
        this.isAsrConnected = false;
      }

      // 清空缓冲区
      this.audioBuffer = [];
      this.lastAudioBuffer = null;

      // 重置状态
      this.isRecording = false;
      this.isSpeaking = false;
      this.silenceCounter = 0;
      this.frameCounter = 0;

      console.log('ASR服务已关闭');
    } catch (error) {
      console.error('关闭ASR服务失败:', error);
      this.onError(error);
    }
  }

  /**
   * 处理音频数据（用于播放机器人回答的音频）
   * @param {string} wavStream - 音频数据（base64编码）
   * @returns {Promise<string>} - 音频文件路径
   */
  async handleWavStream(wavStream) {
    try {
      return await audioManager.handleWavStream(wavStream);
    } catch (error) {
      console.error('处理音频数据失败:', error);
      this.onError(error);
      throw error;
    }
  }

  /**
   * 播放音频
   * @param {string} audioPath - 音频文件路径或ID
   * @param {number} volume - 音量（0-1）
   * @returns {Promise<boolean>} - 播放是否成功
   */
  async playAudio(audioPath, volume = 1) {
    try {
      return await audioManager.addToQueue(audioPath, volume);
    } catch (error) {
      console.error('播放音频失败:', error);
      this.onError(error);
      return false;
    }
  }

  /**
   * 停止播放音频
   * @returns {Promise<void>}
   */
  async stopAudio() {
    try {
      await audioManager.stop();
    } catch (error) {
      console.error('停止播放音频失败:', error);
      this.onError(error);
    }
  }

  /**
   * 获取音频时长
   * @param {string} audioPath - 音频文件路径或ID
   * @returns {Promise<number>} - 音频时长（秒）
   */
  async getAudioDuration(audioPath) {
    try {
      return await audioManager.getAudioDuration(audioPath);
    } catch (error) {
      console.error('获取音频时长失败:', error);
      this.onError(error);
      return 0;
    }
  }

  /**
   * 等待所有音频播放完成
   * @returns {Promise<void>}
   */
  async waitForPlaybackComplete() {
    try {
      return await audioManager.waitForPlaybackComplete();
    } catch (error) {
      console.error('等待音频播放完成失败:', error);
      this.onError(error);
    }
  }

  /**
   * 设置ASR模式
   * @param {string} mode - 'http' 或 'websocket'
   */
  setAsrMode(mode) {
    if (mode === 'http' || mode === 'websocket') {
      this.asrMode = mode;
      console.log(`ASR模式已设置为: ${mode}`);
    } else {
      console.error(`无效的ASR模式: ${mode}，必须是 'http' 或 'websocket'`);
    }
  }
}

// 导出单例
export const chatService = new ChatService();
export const asrChatService = new AsrChatService();

/**
 * 使用示例：
 *
 * // 初始化聊天服务
 * const chatSuccess = await chatService.init({
 *   wsUrl: `${import.meta.env.VITE_APP_AGENT_SOCKET_URL}/chat-tts?access_token=${token}&Tenant-Id=${tenantId}`,
 *   headers: {
 *     Voice: 'zf_xiaobei',
 *     AuthToken: token,
 *     Mode: 'KnowledgeBase',
 *     DeviceType: 'sci_fitness',
 *   },
 *   onMessage: (msg) => {
 *     // 处理消息
 *     console.log('收到消息:', msg);
 *   },
 *   onError: (error) => {
 *     // 处理错误
 *     console.error('聊天服务错误:', error);
 *   }
 * });
 *
 * // 初始化语音识别服务
 * const asrSuccess = await asrChatService.init({
 *   wsUrl: `${import.meta.env.VITE_APP_AGENT_SOCKET_URL}/asr-chat-tts?access_token=${token}&Tenant-Id=${tenantId}`,
 *   httpUrl: `${import.meta.env.VITE_APP_AGENT_HTTP_URL}/audio/asr-stream`,
 *   headers: {
 *     AuthToken: token,
 *     Voice: 'zf_xiaobei',
 *     Mode: 'KnowledgeBase',
 *     DeviceType: 'sci_fitness',
 *     AgentId: 'your_agent_id',
 *     UserId: 'your_user_id',
 *     ConversationId: 'your_conversation_id',
 *     SampleRate: '16000',
 *     StartValue: '4000',
 *     EndValue: '3000',
 *     ChunkSize: '1024',
 *     Role: 'BotA'
 *   },
 *   asrMode: 'websocket', // 或 'http'
 *   onUserSpeechRecognized: (content) => {
 *     // 处理用户语音识别结果
 *     console.log('用户语音识别结果:', content);
 *   },
 *   onBotResponse: (data) => {
 *     // 处理机器人回答
 *     console.log('机器人回答:', data);
 *   },
 *   onFinish: () => {
 *     // 处理对话结束
 *     console.log('对话结束');
 *   },
 *   onError: (error) => {
 *     // 处理错误
 *     console.error('语音识别服务错误:', error);
 *   }
 * });
 *
 * // 发送消息
 * const sendSuccess = await chatService.send({
 *   role: 'user',
 *   content: '你好',
 *   voice: 'zf_xiaobei',
 *   agent_id: 'your_agent_id',
 *   mode: 'KnowledgeBase',
 *   user_id: 'your_user_id',
 *   conversation_id: 'your_conversation_id',
 * });
 *
 * // 开始录音
 * const recordSuccess = await asrChatService.startRecording();
 *
 * // 停止录音
 * asrChatService.stopRecording();
 *
 * // 关闭连接
 * chatService.close();
 * asrChatService.close();
 */

