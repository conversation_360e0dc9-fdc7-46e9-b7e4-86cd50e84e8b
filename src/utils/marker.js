// 自定义水波纹覆盖物
export default function createMarker(TMap, option){
    class markerGif extends TMap.DOMOverlay {
        constructor(options) {
            super(options);
        }

        // 初始化：获取配置参数
        onInit({ position, icon, id, } = {}) {
            Object.assign(this, { position, icon, id, });
        }

        // 创建DOM元素，返回一个Element，使用this.dom可以获取到这个元素
        createDOM() {
            this.onClick = function (e) {
                this.emit('click', e.target.firstChild);
            }.bind(this);
            let mydom = document.createElement('div')
            mydom.setAttribute("id", "grad2")
            mydom.innerHTML = '<div class="biggest"><div class="middle"><img src="' + this.icon+ '"/></div></div>';
            mydom.style.cssText = ['position: absolute;', 'top:  0px;', 'left: 0px;'].join('');
            mydom.addEventListener('click', this.onClick);
            return mydom;
        }

        // 更新DOM元素，在地图移动/缩放后执行
        updateDOM() {
            if (!this.map) {
                return;
            }
            // 经纬度坐标转容器像素坐标
            let pixel = this.map.projectToContainer(this.position);
            // 使饼图中心点对齐经纬度坐标点
            let left = pixel.getX() - this.dom.clientWidth / 2 + 'px';
            let top = pixel.getY() - this.dom.clientHeight / 2 + 'px';
            this.dom.style.transform = `translate(${left}, ${top})`;
        }

        // 销毁时
        onDestroy() {
            if (this.onClick) {
                this.dom.removeEventListener(this.onClick);
            }
            
        }
    }
    
    return new markerGif(option)
}

