/*
 * 异步创建script标签 
 */
export default function MapLoader(key) {
    const TMap_URL = `https://www.dreamdeck.cn:10443/app/icons/operation/map/gljs?v=2.exp&libraries=visualization,tools,service,geometry,model,view&key=${key}&callback=onMapCallback`;
    const location_URL = 'https://mapapi.qq.com/web/mapComponents/geoLocation/v/geolocation.min.js'
    return new Promise((resolve, reject) => {
        // 如果已加载直接返回
        if(typeof TMap !== "undefined") {
            resolve(TMap);
            return true;
        }
        // 地图异步加载回调处理
        window.onMapCallback = function () {
            resolve(TMap);
        };

        // // 插入script脚本
        // let scriptNodeLocation = document.createElement("script");
        // scriptNodeLocation.setAttribute("type", "text/javascript");
        // scriptNodeLocation.setAttribute("src", location_URL);
        // document.body.appendChild(scriptNodeLocation);

        // 插入script脚本
        let scriptNode = document.createElement("script");
        scriptNode.setAttribute("type", "text/javascript");
        scriptNode.setAttribute("src", TMap_URL);
        document.body.appendChild(scriptNode);

        
    })
}