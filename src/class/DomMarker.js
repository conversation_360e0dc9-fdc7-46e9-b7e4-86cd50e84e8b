






function generateMyMarker(TMap) {
  function myMarker(options) {
    TMap.DOMOverlay.call(this, options);
  }
  myMarker.prototype = new TMap.DOMOverlay();

  // 初始化
  myMarker.prototype.onInit = function (options) {
    this.map = options.map;
    this.position = options.position;
    this.data = options.data;
    // this.type = options.type; // 当前marker的类型，是跳动或飞入
  }

  // 创建
  myMarker.prototype.createDOM = function () {
    const container = document.createElement('div');  // 新建一个img的dom
    container.setAttribute('class', 'dom-marker');
    const icon = document.createElement('img');  // 新建一个img的dom
    const name = document.createElement('div');  // 新建一个name的dom
    const triangle = document.createElement('div');  // 新建一个三角的dom
    const triangleBorder = document.createElement('div');  // 新建一个三角的dom
    container.style.userSelect = 'none';
    container.style.position = 'absolute';
    container.style.fontSize = '12px';
    container.style.backgroundColor = '#ffffff';
    container.style.boxShadow = '0 0 1px rgba(0,0,0,.2)';
    container.style.borderRadius = '6px';
    container.style.display = 'flex';
    container.style.alignItems = 'center';
    container.style.cursor = 'pointer';
    container.style.height = '26px';
    // container.style.maxWidth = '160px'
    // container.style.overflow = 'hidden';
    container.style.width = 'max-content';
    container.style.border = '1px solid rgba(13, 116, 86, 0.6)';
    container.style.transition = 'width 0.4s ease-in-out, height 0.4s ease-in-out'
    icon.src = this.data.imgSrc;
    icon.style['-webkit-user-drag'] = 'none';
    icon.style.userSelect = 'none';
    icon.style.pointerEvents = 'auto';
    name.innerText = this.data.name;
    name.style.color = '#053829';
    name.style.overflowY = 'auto';
    name.style.overflowX = 'hidden';
    name.style.height = '98%';
    name.style.display = 'flex'
    name.style.alignItems = 'center';
    triangle.style.width = '0px';
    triangle.style.height = '0px';
    triangle.style.borderStyle = 'solid';
    triangle.style.borderWidth = '10px 8px 0';
    triangle.style.borderColor = '#fff transparent transparent';
    triangle.style.position = 'absolute';
    triangle.style.bottom = '-10px';
    triangle.style.left = '50%';
    triangleBorder.style.width = '0px';
    triangleBorder.style.height = '0px';
    triangleBorder.style.borderStyle = 'solid';
    triangleBorder.style.borderWidth = '10px 8px 0';
    triangleBorder.style.borderColor = 'rgba(13, 116, 86, 0.6) transparent transparent';
    triangleBorder.style.position = 'absolute';
    triangleBorder.style.bottom = '-11px';
    triangleBorder.style.left = '50%';
    if (this.data.iconType === 'img') {
      icon.style.position = 'absolute'
      icon.style.bottom = '0px';
      name.style.marginLeft = '45px';
      icon.style.height = '43px';
      icon.style.width = 'auto';
      container.style.padding = '2px 6px';
      icon.style.boxShadow = '0 0 1px rgba(0,0,0,.2)';
    } else {
      icon.style.width = '38px';
      icon.style.height = '43px';
      container.style.padding = '2px 6px 2px 0px';
    }
    container.appendChild(triangleBorder);
    container.appendChild(triangle);
    container.appendChild(icon);
    container.appendChild(name);
    // click事件监听
    this.onClick = () => {
      // DOMOverlay继承自EventEmitter，可以使用emit触发事件
      this.emit('click', { data: this.data, marker: this });
    };
    // pc端注册click事件，移动端注册touchend事件
    container.addEventListener('click', this.onClick);
    return container;
  }

  // 更新DOM元素，在地图移动/缩放后执行
  myMarker.prototype.updateDOM = function () {
    if (!this.map) { return; }
    let pixel = this.map.projectToContainer(this.position); // 经纬度坐标转容器像素坐标
    let left = pixel.getX() - this.dom.clientWidth / 2 + 'px';
    let top = pixel.getY() - this.dom.clientHeight + 'px';
    // 使用top/left将DOM元素定位到指定位置
    this.dom.style.top = top;
    this.dom.style.left = left;
  }
  // 销毁时需解绑事件监听
  myMarker.prototype.onDestroy = function () {
    if (this.onClick) {
      this.dom.removeEventListener('click', this.onClick);
    }
  };
  return myMarker;
}


export default function renderMyMarker(poiList = [], TMap, map) {
  if (!poiList) return [];
  const MyMarker = generateMyMarker(TMap);
  const myMarkers = poiList.map(marker => {
    const myMarker = new MyMarker({
      map,
      position: new TMap.LatLng(parseFloat(marker.latitude), parseFloat(marker.longitude)),
      data: {
        name: marker.name,
        imgSrc: marker.icon
          ? `${process.env.VUE_APP_BASE_API}/admin/file/image/${marker.icon}`
          : require("@/assets/typechecked.png"),
        iconType: marker.icon ? 'img' : 'icon',
        id: marker.id
      },
      isStopPropagation: true
    });
    return myMarker;
  });
  

  return myMarkers
}