






function generateImgMarker(TMap) {
  function imgMarker(options) {
    TMap.DOMOverlay.call(this, options);
  }
  imgMarker.prototype = new TMap.DOMOverlay();

  // 初始化
  imgMarker.prototype.onInit = function (options) {
    this.map = options.map;
    this.position = options.position;
    this.data = options.data;
    // this.type = options.type; // 当前marker的类型，是跳动或飞入
  }

  // 创建
  imgMarker.prototype.createDOM = function () {
    const img = document.createElement('img');  // 新建一个img的dom
    img.src = this.data.imgSrc;
    img.style['-webkit-user-drag'] = 'none';
    img.style.userSelect = 'none';
    img.style.pointerEvents = 'auto';
    img.style.position = 'absolute';
    img.style.width = '100px'
    img.style.height = '100px'
    // click事件监听
    this.onClick = () => {
      // DOMOverlay继承自EventEmitter，可以使用emit触发事件
      this.emit('click', { data: this.data, marker: this });
    };
    // pc端注册click事件，移动端注册touchend事件
    img.addEventListener('click', this.onClick);
    return img;
  }

  // 更新DOM元素，在地图移动/缩放后执行
  imgMarker.prototype.updateDOM = function () {
    if (!this.map) { return; }
    let pixel = this.map.projectToContainer(this.position); // 经纬度坐标转容器像素坐标
    let left = pixel.getX() - this.dom.clientWidth / 2 + 'px';
    let top = pixel.getY() - this.dom.clientHeight + 'px';
    // 使用top/left将DOM元素定位到指定位置
    this.dom.style.top = top;
    this.dom.style.left = left;
  }
  // 销毁时需解绑事件监听
  imgMarker.prototype.onDestroy = function () {
    if (this.onClick) {
      this.dom.removeEventListener('click', this.onClick);
    }
  };
  return imgMarker;
}


export default function renderImgMarker(obj, TMap, map) {
  const ImgMarker = generateImgMarker(TMap);
  const imgMarker = new ImgMarker({
    map,
    position: new TMap.LatLng(parseFloat(obj.latitude), parseFloat(obj.longitude)),
    data: {
      imgSrc: obj.img,
      id: obj.id,
      singleAgentId: obj.singleAgentId,
      staticImg: obj.img2
    },
    isStopPropagation: true
  });
  // imgMarker.on('click', markerClick);
  return imgMarker
}

// const markerClick = function (e) {
//   console.log('点位被点击了', e);
// };