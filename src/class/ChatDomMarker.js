






function generateMyMarker(TMap) {
  function myMarker(options) {
    TMap.DOMOverlay.call(this, options);
  }
  myMarker.prototype = new TMap.DOMOverlay();

  // 初始化
  myMarker.prototype.onInit = function (options) {
    this.map = options.map;
    this.position = options.position;
    this.data = options.data;
    // this.type = options.type; // 当前marker的类型，是跳动或飞入
  }

  // 创建
  myMarker.prototype.createDOM = function () {
    const container = document.createElement('div');  // 新建一个img的dom
    const name = document.createElement('div');  // 新建一个img的dom
    const msg = document.createElement('div');  // 新建一个img的dom
    const triangle = document.createElement('div');  // 新建一个三角的dom
    const triangleBorder = document.createElement('div');  // 新建一个三角的dom

    container.setAttribute('class','chat-dom-marker')
    container.style.width = '200px'
    container.style['-webkit-user-drag'] = 'none';
    container.style.userSelect = 'none';
    container.style.pointerEvents = 'auto';
    container.style.position = 'absolute';
    container.style.border = '2px solid #3DCBE1';
    container.style['border-radius'] = '10px'
    container.style.padding = '5px'
    container.style.zIndex = 2
    container.style.background = 'linear-gradient(180deg, #017B84 0%, rgba(36, 240, 255, 0.23) 100%)';

    name.style.color = '#fff'
    name.style.width = 'fit-content'
    name.style.backgroundColor = '#36BAD4'
    name.style.padding = '2px 11px'
    name.style['border-radius'] = '10px'
    name.style['font-size'] = '12px'
    name.innerText = this.data.name

    // msg.innerHTML = this.data.msg
    msg.style.color = '#fff'
    msg.style.marginTop = '5px'
    msg.style['font-size'] = '10px'
    msg.style.height = '50px'
    msg.style.overflow = 'auto'

    triangle.style.width = '0px';
    triangle.style.height = '0px';
    triangle.style.borderStyle = 'solid';
    triangle.style.borderWidth = '10px 8px 0';
    triangle.style.borderColor = '#fff transparent transparent';
    triangle.style.position = 'absolute';
    triangle.style.bottom = '-14px';
    triangle.style['-webkit-transform'] = 'translateX(-8px)';
    triangle.style.left = '50%';
    triangleBorder.style.width = '0px';
    triangleBorder.style.height = '0px';
    triangleBorder.style.borderStyle = 'solid';
    triangleBorder.style.borderWidth = '10px 8px 0';
    triangleBorder.style.borderColor = '#3dcbe1 transparent transparent';
    triangleBorder.style.position = 'absolute';
    triangleBorder.style.bottom = '-11px';
    triangleBorder.style['-webkit-transform'] = 'translateX(-8px)';
    triangleBorder.style.left = '50%';

    container.appendChild(name)
    container.appendChild(msg)
    container.appendChild(triangle)
    container.appendChild(triangleBorder)
    return container;
  }

  // 更新DOM元素，在地图移动/缩放后执行
  myMarker.prototype.updateDOM = function () {
    if (!this.map) { return; }
    let pixel = this.map.projectToContainer(this.position); // 经纬度坐标转容器像素坐标
    let left = pixel.getX() - this.dom.clientWidth / 2 + 'px';
    let top = pixel.getY() - (this.dom.clientHeight + 110) + 'px';
    // 使用top/left将DOM元素定位到指定位置
    this.dom.style.top = top;
    this.dom.style.left = left;
  }
  // 销毁时需解绑事件监听
  myMarker.prototype.onDestroy = function () {
    if (this.onClick) {
      this.dom.removeEventListener('click', this.onClick);
    }
  };
  return myMarker;
}


export default function renderImgMarker(obj,msgId,TMap, map) {
  const MyMarker = generateMyMarker(TMap);
  const myMarker = new MyMarker({
    map,
    position: new TMap.LatLng(parseFloat(obj.latitude), parseFloat(obj.longitude)),
    data: {
      // msg: msg,
      id: msgId,
      name:obj.name
    },
    isStopPropagation: true
  });
  myMarker.on('click', markerClick);
  return myMarker
}

const markerClick = function (e) {
  console.log('点位被点击了', e);
};