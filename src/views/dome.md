<template>
    <!-- 主容器 -->
    <div class="container">
      <!-- 顶部导航栏 -->
      <top-bar back color="#000" :back="showBack" position="relative" background="transparent"
        @getHeight="getTopBarHeight">
        <text class="group-name" :style="{ 'margin-left': showBack ? '80rpx' : '0' }">智能体群聊</text>
      </top-bar>
  
      <!-- 背景装饰 -->
      <div class="background">
        <div class="background-round1"></div>
        <div class="background-round2"></div>
        <div class="background-round3"></div>
      </div>
  
      <!-- 智能体列表 -->
      <scroll-view scroll-x class="member-list">
        <div class="member-list-content">
          <div class="member-item" v-for="item in memberList" :key="item.singleAgentId" @click="navInfoChat(item)">
            <image class="avatar" :src="item.avatar" mode="widthFix" />
            <div class="name">{{ item.name }}</div>
            <div class="sub-title" :style="{ color: item.color, background: item.color + '38' }">
              {{ item.subTitle }}
            </div>
          </div>
        </div>
      </scroll-view>
  
      <!-- 聊天主区域 -->
      <div class="chat-container" :style="[...chatStyle]">
        <!-- 聊天背景 -->
        <img :src="`${imgUrl}/agent/chat-group-bg.png`" alt="" class="chat-bg" />
  
        <!-- 消息列表区域 -->
        <scroll-view scroll-y scroll-with-animation enable-flex id="scroll" class="chat-container-scroll"
          :scroll-top="scrollTop" @scroll="scroll">
          <div id="scroll-view-content" class="scroll-content">
            <!-- 开场白消息 -->
            <div class="message-item">
              <div class="message-content other">
                <image class="avatar" :src="prologue.avatar" mode="widthFix" />
                <div class="message-body">
                  <text class="username">{{ prologue.username }}</text>
                  <div class="message-bubble">{{ prologue.content || '...' }}</div>
                </div>
              </div>
            </div>
  
            <!-- 聊天消息列表 -->
            <div class="message-item" v-for="(item, index) in logList" :key="item.id">
              <div :class="['message-content', item.type === 0 ? 'self' : 'other']">
                <image class="avatar" :src="item.avatar" mode="widthFix" />
                <div class="message-body">
                  <text class="username" v-if="item.type === 1">{{ item.username }}</text>
                  <div class="message-bubble">
                    {{ item.msg || '...' }}
                    <view v-if="item.type === 1" class="voice-control" @click="playVoice(index)">
                      <u-icon :name="item.voicePlay ? 'pause' : 'play-right-fill'" :size="20" color="#fff"></u-icon>
                      <view>{{ (item.voiceTime || 0).toFixed(2) }}s</view>
                    </view>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </scroll-view>
  
        <!-- 快捷问题区域 -->
        <div class="question-container">
          <div :class="['question-item', item.type === 1 ? 'question' : 'at']" v-for="(item, index) in questionList"
            :key="index" @click="item.type === 1 ? sendQuestion(item) : openAt()">
            {{ item.content }}
          </div>
        </div>
  
        <!-- 消息输入区域 -->
        <chat-sender ref="chatSender" @getMsg="sendMessage" @input="handleInput" class="chat-sender" />
      </div>
  
      <!-- @功能弹窗 -->
      <u-popup :show="showPopup" bgColor="transparent">
        <view class="at-popup">
          <image :src="`${imgUrl}/agent/at-top.png`" mode="widthFix" class="at-top" @click="atFinish" />
          <view class="at-content">
            <!-- @弹窗头部 -->
            <view class="at-content-header">
              <view class="at-content-header-right">
                <view :class="['at-content-header-right-text', atList.length > 0 ? 'is-finish' : '']" @click="atFinish">
                  {{ atList.length > 0 ? '完成' : '取消' }}
                </view>
              </view>
              <view class="at-content-header-title">选择提醒的人</view>
              <view class="at-content-header-right" @click="allChoose">
                <view>全选</view>
                <view class="choose-btn" :class="{ 'is-all': atIsAll }"></view>
              </view>
            </view>
  
            <!-- @弹窗内容区 -->
            <view class="at-content-body">
              <scroll-view scroll-y class="at-content-body-scroll">
                <view class="at-content-body-scroll-item" v-for="item in atStaticList" :key="item.singleAgentId"
                  @click="chooseMember(item)">
                  <view class="left">
                    <image :src="item.avatar" mode="widthFix" class="avatar" />
                    <view class="name">{{ item.name }}</view>
                  </view>
                  <view class="choose-btn" :class="{ 'is-at': item.isAt }"></view>
                </view>
              </scroll-view>
            </view>
          </view>
        </view>
      </u-popup>
    </div>
  </template>
  
  <script>
  import TopBar from "@/pages_agent/components/TopBar";
  import ChatSender from "@/pages_agent/components/ChatSender";
  import { imgUrl, webSocketUrl, webSocketUrlOld } from "@/pages_agent/utils/config";
  import { generateUUID } from '@/utils/utils'
  import { getSingleAgentInfo, getCueWordIdInfo, speak } from "@/pages_agent/api/agent";
  import { chatService, asrChatService } from "@/pages_agent/utils/asrChatService";
  import { MessageService } from "@/pages_agent/utils/messageService";
  import { RecordingService } from "@/pages_agent/utils/recordingService";
  import { audioManager } from "@/pages_agent/utils/audioManager";
  import websocketUtil from '@/pages_agent/utils/websocket';
  const agentList = [
    {
      id: 0,
      singleAgentId: "1870410461283876864",
      name: "灵宝",
      avatar: `${imgUrl}/agent/lingbao-avatar.png`,
      subTitle: "市属公园管理中心",
      color: "#FA518D",
      isAt: false,
      voice: 'zf_xiaobei',
    },
    {
      id: 1,
      singleAgentId: "1873989368805326848",
      name: "大福",
      avatar: `${imgUrl}/agent/dafu-avatar.png`,
      subTitle: "天坛公园",
      color: "#834E97",
      isAt: false,
      voice: 'zf_xiaobei',
    },
    {
      id: 2,
      singleAgentId: "1873991412098269184",
      name: "春桃",
      avatar: `${imgUrl}/agent/chuntao-avatar.png`,
      subTitle: "植物园",
      color: "#FF9696",
      isAt: false,
      voice: 'zf_xiaobei',
    },
    {
      id: 3,
      singleAgentId: "1873989525852651520",
      name: "大象",
      avatar: `${imgUrl}/agent/daxiang-avatar.png`,
      subTitle: "陶然亭",
      color: "#3288D8",
      isAt: false,
    },
    {
      id: 4,
      singleAgentId: "1873989313620869120",
      name: "萌兰三太子",
      avatar: `${imgUrl}/agent/menglan-avatar.png`,
      subTitle: "动物园",
      color: "#2DC535",
      isAt: false,
      voice: 'zf_xiaobei',
    },
    {
      id: 5,
      singleAgentId: "1873989425034166272",
      name: "小樱",
      avatar: `${imgUrl}/agent/xiaoying-avatar.png`,
      subTitle: "玉渊潭公园",
      color: "#EE80A4",
      isAt: false,
      voice: 'zf_xiaobei',
    },
    {
      id: 6,
      singleAgentId: "1873989250479816704",
      name: "吉瑞宝贝",
      avatar: `${imgUrl}/agent/jiribao-avatar.png`,
      subTitle: "颐和园",
      color: "#71B1E7",
      isAt: false,
      voice: 'zf_xiaobei',
    },
    {
      id: 7,
      singleAgentId: "1878700713102540800",
      name: "鸭鸭",
      avatar: `${imgUrl}/agent/yaya-avatar.png`,
      subTitle: "北海公园",
      color: "#075434",
      isAt: false,
      voice: 'zf_xiaobei',
    },
    {
      id: 8,
      singleAgentId: "1878700851611041792",
      name: "红枫侠客",
      avatar: `${imgUrl}/agent/xiake-avatar.png`,
      subTitle: "香山公园",
      color: "#F93E00",
      isAt: false,
      voice: 'zf_xiaobei',
    },
    {
      id: 9,
      singleAgentId: "1878700919885922304",
      name: "牡丹仙子",
      avatar: `${imgUrl}/agent/mudanxianzi-avatar.png`,
      subTitle: "景山公园",
      color: "#F8784B",
      isAt: false,
      voice: 'zf_xiaobei',
    },
    {
      id: 10,
      singleAgentId: "1878700770509979648",
      name: "银杏仙子",
      avatar: `${imgUrl}/agent/yinxingxianzi-avatar.png`,
      subTitle: "中山公园",
      color: "#F9F500",
      isAt: false,
      voice: 'zf_xiaobei',
    },
    {
      id: 11,
      singleAgentId: "1878700988425043968",
      name: "竹福",
      avatar: `${imgUrl}/agent/zhufu-avatar.png`,
      subTitle: " 紫竹院",
      color: "#84CC52",
      isAt: false,
      voice: 'zf_xiaobei',
    },
  ];
  export default {
    name: "GroupChat",
    components: { TopBar, ChatSender },
  
    /**
     * 组件数据
     * @return {Object} 组件数据对象
     */
    data: () => ({
      // 基础配置
      singleAgentId: "1874651874875932672", // 默认智能体ID 1874651874875932672
      imgUrl, // 图片资源路径
      memberList: agentList, // 智能体列表
  
      // UI状态
      chatStyle: { height: "" }, // 聊天容器样式
      scrollTop: 0, // 滚动位置
      scrollHeight: 0, // 滚动区域高度
      isLoading: false, // 加载状态
      hasMore: true, // 是否有更多消息
      showBack: false,
  
      // 群组信息
      groupId: "12345", // 群组ID
      groupName: "智能体群聊", // 群组名称
      memberCount: 4, // 成员数量
      showPopup: false, // @弹窗显示状态
  
      // 当前会话状态
      currentChat: {
        singleAgentIds: [], // 当前对话的智能体ID列表
        index: 0, // 当前对话的智能体索引
        msgId: '', // 消息ID
        msg: '', // 当前消息内容
        question: '', // 当前问题
        isFinish: true // 是否完成对话
      },
  
      // 消息相关
      logList: [], // 消息列表
      questionList: [], // 快捷问题列表
      inputVal: '', // 输入框值
      prologue: null, // 开场白
  
      // WebSocket实例
      socket: null,
      socketOld: null,
  
      // @功能相关
      atStaticList: agentList.slice(1, agentList.length), // 可@的智能体列表
      atList: [], // 已选择的@列表
      atIsAll: false, // 是否全选
  
      // 常量配置
      TIMESTAMP_INTERVAL: 5 * 60 * 1000, // 时间戳显示间隔(5分钟)
      chatConfig: {
        user_id: '',
        conversation_id: '',
        voice: 'zf_xiaobei',
        mode: 'KnowledgeBase',
        deviceType: 'sci_fitness'
      },
    }),
    onLoad() {
      const pages = getCurrentPages();
      this.showBack = pages.length > 1
      console.log(this.showBack);
      this.chatConfig.user_id = generateUUID()
      this.chatConfig.conversation_id = generateUUID()
    },
    /**
     * 组件创建时初始化服务
     */
    created() {
      // 初始化消息处理服务
      this.messageService = new MessageService(this);
  
      // 初始化录音服务
      this.recordingService = new RecordingService(this);
  
      console.log('服务初始化完成');
    },
  
    /**
     * 生命周期 - 页面显示时
     */
    onShow() {
      this.initChat();
    },
  
    /**
     * 生命周期 - 页面隐藏时
     */
    onHide() {
      // this.cleanup();
    },
  
    methods: {
      // 导航到单个聊天页面
      navInfoChat(item) {
        uni.navigateTo({
          url: `/pages_agent/pages/chat/index?singleAgentId=${item.singleAgentId}`
        });
      },
  
      // 发送预定义问题
      async sendQuestion(item) {
        this.sendStop(); // 停止当前聊天
        this.currentChat.singleAgentIds = [this.singleAgentId];
        this.currentChat.index = 0;
        this.currentChat.question = item.content;
        this.currentChat.msg = '';
  
        // 使用预处理流程发送消息
        await this.sendPreprocessMessage(item.content);
  
        this.scrollToLower(); // 滚动到最新消息
      },
  
      // 发送停止信号
      sendStop() {
        if (!this.currentChat.isFinish) {
          // 使用新的消息格式发送停止信号
          this.socket.send({
            role: "user",
            content: "<Finish>",
            agent_id: this.currentChat.singleAgentIds[this.currentChat.index],
            voice: this.chatConfig.voice, // 默认语音
            mode: this.chatConfig.mode, // 默认模式
            user_id: this.chatConfig.user_id,
            conversation_id: this.chatConfig.conversation_id
          });
        }
      },
  
      /**
       * 处理WebSocket接收到的消息
       * @param {Object} msg - WebSocket消息对象
       */
      async handleMessage(msg) {
        try {
          // 检查msg和msg.data是否存在
          if (!msg || !msg.data) {
            console.error("收到无效的WebSocket消息:", msg);
            return;
          }
  
          // 尝试解析JSON数据
          let data;
          try {
            // 检查msg.data是否为字符串且不为空
            if (typeof msg.data === 'string' && msg.data.trim() !== '') {
              data = JSON.parse(msg.data);
            } else if (typeof msg.data === 'object') {
              // 如果已经是对象，直接使用
              data = msg.data;
            } else {
              console.error("无法解析的WebSocket消息数据类型:", typeof msg.data);
              return;
            }
          } catch (parseError) {
            console.error("解析WebSocket消息失败:", parseError, "原始数据:", msg.data);
            return;
          }
  
          // 检查解析后的数据是否有效
          if (!data) {
            console.error("解析后的WebSocket消息数据为空");
            return;
          }
  
          console.log("解析后的WebSocket消息数据:", data);
  
          // 如果是第一条消息且是用户消息，停止当前播放
          if (this.logList.length > 0 && this.logList[this.logList.length - 1].type === 0) {
            // 停止当前播放
            this.resetAllVoicePlay();
            await audioManager.stop();
          }
  
          // 使用messageService处理消息
          switch (data.content) {
  
            case "<Finish>":
              // 处理完成消息
              // 使用 waitForPlaybackComplete 方法等待所有音频播放完成
              const lastMessage = this.logList[this.logList.length - 1]
              this.currentChat.isFinish = true;
  
              audioManager
                  .waitForPlaybackComplete()
                  .then(() => {
                    console.log("所有音频播放完成，重置播放状态");
                    lastMessage.voicePlay = false;
                    // 如果有下一个智能体需要对话，继续发送消息
                    this.nextMessage()
                  })
                  .catch((err) => {
                    // 如果有下一个智能体需要对话，继续发送消息
                    lastMessage.voicePlay = false;
                    this.nextMessage()
                  });
              break;
  
            default:
              // 处理常规消息
              await this.handleBotResponse(data);
              break;
          }
        } catch (error) {
          console.error("处理消息失败:", error);
          uni.showToast({
            title: "处理消息失败",
            icon: "none",
          });
        }
      },
  
      nextMessage() {
        if (this.currentChat.singleAgentIds.length > 0) {
          if (this.currentChat.index < this.currentChat.singleAgentIds.length - 1) {
            this.currentChat.index += 1;
            this.currentChat.msg = '';
            this.sendChatMessage();
          } else {
            this.resetCurrentChat();
          }
        } else {
          this.resetCurrentChat();
        }
      },
  
      // 发送聊天消息
      async sendChatMessage() {
        try {
          // 停止当前播放
          await this.messageService.stopCurrentPlayback();
  
          // 使用新的消息格式发送消息
          const success = await this.socket.send({
            role: "user",
            content: this.currentChat.question,
            agent_id: this.currentChat.singleAgentIds[this.currentChat.index],
            voice: this.chatConfig.voice, // 默认语音
            mode: this.chatConfig.mode, // 默认模式
            user_id: this.chatConfig.user_id,
            conversation_id: this.chatConfig.conversation_id
          });
  
          if (success) {
            console.log("消息发送成功");
          } else {
            console.error("消息发送失败");
            uni.showToast({
              title: "消息发送失败",
              icon: "none",
            });
          }
  
          // 滚动到最新消息
          this.scrollToLower();
        } catch (error) {
          console.error("发送聊天消息失败:", error);
          uni.showToast({
            title: "发送消息失败",
            icon: "none",
          });
        }
      },
  
      // 重置当前聊天状态
      resetCurrentChat() {
        this.currentChat.index = 0;
        this.currentChat.msg = '';
        this.currentChat.singleAgentIds = [this.singleAgentId];
      },
  
      // 清理资源
      cleanup() {
        console.log('清理资源', this.socket);
  
        // 关闭聊天服务
        if (this.socket) this.socket.close();
  
        // 关闭语音识别服务
        asrChatService.close();
        this.isAsrConnected = false;
  
        // 停止当前对话
        if (!this.currentChat.isFinish) this.sendStop();
  
        // 清理音频资源
        audioManager.stop();
      },
  
      /**
       * 处理语音输入（从ASR WebSocket接收到的不包含wav_stream的消息）
       * @param {string} content - 语音识别的文本内容
       */
      async handleVoiceInput(content) {
        try {
          if (!content) {
            console.log("语音识别内容为空，不处理");
            return;
          }
  
          console.log("处理语音输入:", content);
  
          // 停止当前播放
          await this.messageService.stopCurrentPlayback();
  
          // 添加用户消息到消息列表
          // this.logList.push({
          //   id: Date.now().toString(),
          //   msg: content,
          //   timestamp: new Date().getTime(),
          //   type: 0, // 0表示用户消息
          //   username: "用户123",
          //   avatar: uni.getStorageSync('avatar'),
          // });
  
          this.sendMessage(content);
  
          this.scrollToLower();
        } catch (error) {
          console.error("处理语音输入失败:", error);
          uni.showToast({
            title: "处理语音输入失败",
            icon: "none",
          });
        }
      },
  
      /**
       * 处理机器人回答（从ASR WebSocket接收到的带有wav_stream的消息）
       * @param {Object} data - 包含content和wav_stream的数据对象
       */
      async handleBotResponse(data) {
        try {
          // 标记对话未完成
          this.currentChat.isFinish = false;
  
          // 获取当前应该回复的智能体
          const currentAgentId = this.currentChat.singleAgentIds[this.currentChat.index];
          const currentAgent = this.memberList.find(item =>
            item.singleAgentId === currentAgentId);
  
          if (!currentAgent) {
            console.error("找不到对应的智能体:", currentAgentId);
            return;
          }
  
          // 检查最后一条消息是否是当前智能体的回复
          const lastMessage = this.logList.length > 0 ? this.logList[this.logList.length - 1] : null;
          const needNewMessage = !lastMessage ||
                                lastMessage.type === 0 || // 上一条是用户消息
                                (lastMessage.type === 1 && lastMessage.username !== currentAgent.name); // 上一条是其他智能体的消息
  
          // 如果需要创建新消息
          if (needNewMessage) {
            console.log("添加机器人回复消息:", currentAgent.name);
            this.logList.push({
              id: Date.now().toString(),
              msg: '',
              timestamp: new Date().getTime(),
              type: 1, // 1表示机器人消息
              username: currentAgent.name,
              avatar: currentAgent.avatar,
              voicePlay: true,
              voiceTime: 0,
              audio_urls: []
            });
          }
  
          // 更新消息内容
          if (data.content) {
            // 获取最后一条消息
            const lastMessage = this.logList[this.logList.length - 1];
  
            // 确保最后一条消息是当前智能体的消息
            if (lastMessage && lastMessage.type === 1 && lastMessage.username === currentAgent.name) {
              lastMessage.msg = (lastMessage.msg || '') + data.content;
            } else {
              console.error("无法更新消息内容：最后一条消息不是当前智能体的消息");
            }
          }
  
          // 处理音频
          if (data.wav_stream) {
            try {
              // 获取最后一条消息
              const lastMessage = this.logList[this.logList.length - 1];
  
              // 确保最后一条消息是当前智能体的消息
              if (lastMessage && lastMessage.type === 1 && lastMessage.username === currentAgent.name) {
                // 处理音频流
                const audioPath = await audioManager.handleWavStream(data.wav_stream);
                console.log("音频处理完成:", audioPath);
  
                // 添加到消息的音频列表
                if (!lastMessage.audio_urls) {
                  lastMessage.audio_urls = [];
                }
                lastMessage.audio_urls.push(audioPath);
  
                // 更新音频时长
                try {
                  const duration = await this.getAudioDuration(audioPath);
                  lastMessage.voiceTime = (lastMessage.voiceTime || 0) + duration;
                  console.log("更新音频时长:", lastMessage.voiceTime);
                } catch (durationError) {
                  console.error("获取音频时长失败:", durationError);
                }
  
                // 播放音频
                await audioManager.play(audioPath);
              } else {
                console.error("无法处理音频：最后一条消息不是当前智能体的消息");
              }
            } catch (error) {
              console.error("处理音频失败:", error);
            }
          }
  
          this.scrollToLower();
        } catch (error) {
          console.error("处理机器人回答失败:", error);
          uni.showToast({
            title: "处理机器人回答失败",
            icon: "none",
          });
        }
      },
  
      // 滚动到最新消息
      scrollToLower() {
        try {
          this.$nextTick(() => {
            uni.createSelectorQuery().in(this).select('#scroll-view-content')
              .boundingClientRect(res => {
                if (res) {
                  this.scrollTop = res.height;
                }
              }).exec();
          });
        } catch (error) {
          console.error('滚动失败:', error);
        }
      },
  
      /**
       * 播放或停止语音
       * @param {Number} index - 消息索引
       */
      async playVoice(index) {
        try {
          const currentItem = this.logList[index];
          console.log("点击播放按钮:", currentItem);
  
          // 判断如果没有音频则不播放
          if (
            !currentItem ||
            !currentItem.audio_urls ||
            currentItem.audio_urls.length === 0 ||
            currentItem.voiceTime <= 0
          ) {
            console.log("没有可播放的音频");
            uni.showToast({ title: "没有可播放的音频", icon: "none" });
            return;
          }
  
          // 如果当前项目正在播放，则停止播放
          if (currentItem.voicePlay) {
            console.log("停止当前播放");
            audioManager.stop();
            currentItem.voicePlay = false;
            return;
          }
  
          // 停止所有正在播放的音频并重置状态
          this.resetAllVoicePlay();
  
          // 等待audioManager完全停止并重置状态
          console.log("等待audioManager完全停止并重置状态...");
          await audioManager.stop();
  
          // 设置当前项的播放状态
          currentItem.voicePlay = true;
  
          // 禁用超时处理，防止音频播放被超时中断
          audioManager.setTimeoutEnabled(false);
  
          // 使用循环Promise的方式播放音频，不使用队列
          console.log("开始播放音频，总数:", currentItem.audio_urls.length);
  
          try {
            // 依次播放所有音频
            for (
              let i = 0;
              i < currentItem.audio_urls.length && currentItem.voicePlay;
              i++
            ) {
              const audioPath = currentItem.audio_urls[i];
              console.log(
                `播放音频 ${i + 1}/${currentItem.audio_urls.length}:`,
                audioPath
              );
  
              // 直接播放音频，不使用队列
              await audioManager.playDirectly(audioPath, 1);
  
              // 如果播放过程中停止了，则中断循环
              if (!currentItem.voicePlay) {
                console.log("播放过程中停止了，中断循环");
                break;
              }
            }
  
            // 检查当前项是否仍在播放状态
            if (currentItem.voicePlay) {
              console.log("所有音频播放完成，重置状态");
              currentItem.voicePlay = false;
            }
          } catch (error) {
            console.error("音频播放过程中出错:", error);
            // 重置播放状态
            if (currentItem.voicePlay) {
              currentItem.voicePlay = false;
            }
          } finally {
            // 恢复超时处理的设置
            audioManager.setTimeoutEnabled(true);
          }
        } catch (error) {
          console.error("播放语音失败:", error);
          uni.showToast({ title: "播放语音失败", icon: "none" });
  
          // 重置播放状态
          if (index >= 0 && this.logList[index]) {
            this.logList[index].voicePlay = false;
          }
        }
      },
  
      /**
       * 重置所有音频播放状态
       */
      resetAllVoicePlay() {
        this.logList.forEach(item => {
          if (item.voicePlay) {
            item.voicePlay = false;
          }
        });
      },
  
      /**
       * 完成@人员选择
       * 将选中的人员添加到输入框中
       */
      atFinish() {
        this.$nextTick(() => {
          if (this.atList.length > 0) {
            const chatSender = this.$refs.chatSender;
            const atNames = this.atList.map(item => '@' + item.name).join(' ');
            chatSender.inputVal = atNames + ` ${chatSender.inputVal}`;
            this.showPopup = false;
          } else {
            this.showPopup = false;
          }
        })
      },
  
      /**
       * 选择@的成员
       * @param {Object} agent - 被选择的智能体信息
       */
      chooseMember(agent) {
        // 切换选中状态
        agent.isAt = !agent.isAt;
        const atAgent = this.atList.findIndex(item => item.singleAgentId === agent.singleAgentId);
  
        // 更新选中列表
        if (atAgent !== -1) {
          this.atList.splice(atAgent, 1);
        } else {
          this.atList.push(agent);
        }
  
        // 更新全选状态
        const atList = [];
        this.atStaticList.forEach(item => {
          if (item.singleAgentId === agent.singleAgentId) {
            item.isAt = agent.isAt;
          }
          atList.push(item.isAt);
        });
        this.atIsAll = !atList.includes(false);
      },
  
      /**
       * 全选/取消全选@列表中的成员
       */
      allChoose() {
        this.atIsAll = !this.atIsAll;
        this.atStaticList.forEach(item => {
          item.isAt = this.atIsAll;
        });
        this.atList = this.atStaticList.filter(item => item.isAt);
      },
  
      /**
       * 处理输入框的输入事件
       * 处理@功能的触发和删除
       * @param {Object} e - 输入事件对象
       */
      handleInput(e) {
        const str = e.detail.value;
        const cursorPosition = e.detail.cursor;
        const cursorBeforeStr = str.slice(0, cursorPosition);
        const chatSender = this.$refs.chatSender;
  
        // 检测是否触发@功能
        if (cursorBeforeStr.endsWith('@') && str.length >= this.inputVal.length) {
          chatSender.inputVal = str.slice(cursorBeforeStr, -1);
          this.openAt();
        } else {
          // 检测是否删除@的人员
          if (this.atList.length > 0) {
            this.atList.forEach(item => {
              if (cursorBeforeStr.endsWith(`@${item.name}`)) {
                chatSender.inputVal = str.replace(`@${item.name}`, '');
              }
            });
          }
        }
        this.inputVal = str;
      },
  
      /**
       * 打开@人员选择弹窗
       */
      openAt() {
        this.showPopup = true;
        this.atIsAll = false;
        // 重置所有选择状态
        this.atStaticList.forEach(item => {
          item.isAt = false;
        });
        this.atList = [];
      },
  
      /**
       * 获取顶部栏高度并设置聊天容器高度
       * @param {Number} height - 顶部栏高度
       */
      getTopBarHeight(height) {
        this.chatStyle.height = `calc(100vh - ${height}px - 162px)`;
      },
  
      /**
       * 初始化聊天
       * 获取智能体信息和提示词信息
       */
      async initChat() {
        try {
          const agentInfo = await getSingleAgentInfo(this.singleAgentId);
          const cueWordInfo = await getCueWordIdInfo(agentInfo.cueWordId);
  
          // 设置开场白
          this.prologue = {
            avatar: `${imgUrl}/agent/lingbao-avatar.png`,
            username: "灵宝",
            content: cueWordInfo.prologue,
            type: 1, // 1表示机器人消息
            timestamp: new Date().getTime()
          }
  
          // 设置快捷问题列表
          this.questionList = Object.values(cueWordInfo.example)
            .map((i) => ({ type: 1, content: i }))
            .concat([{ type: 2, content: "可以@你想对话的人" }]);
  
          // 初始化WebSocket连接
          await this.initWebSocket();
  
        } catch (error) {
          console.error("初始化聊天失败:", error);
          uni.showToast({
            title: "初始化聊天失败",
            icon: "none",
          });
        }
      },
  
      /**
       * 初始化WebSocket连接
       */
      async initWebSocket() {
        try {
          const token = uni.getStorageSync('token');
          const tenantId = uni.getStorageSync('sysTenantId');
  
          // 构建WebSocket URL
          const wsUrl = `${webSocketUrl}/audio/chat-tts?access_token=${token}&Tenant-Id=${tenantId}`;
  
          // 构建头信息
          const headers = {
            Voice: this.chatConfig.voice,
            AuthToken: token,
            Mode: this.chatConfig.mode,
            DeviceType: this.chatConfig.deviceType,
          };
  
          // 初始化聊天服务
          const success = await chatService.init({
            wsUrl,
            headers,
            onMessage: this.handleMessage,
            onError: (error) => {
              console.error("聊天服务错误:", error);
              uni.showToast({
                title: "聊天服务错误",
                icon: "none"
              });
            }
          });
  
          if (success) {
            console.log("聊天服务初始化成功");
            this.socket = chatService; // 保持兼容性
          } else {
            console.error("聊天服务初始化失败");
            uni.showToast({
              title: "聊天服务初始化失败",
              icon: "none"
            });
          }
  
          
  
          // 同时初始化语音识别 WebSocket
          await this.initAsrWebSocket();
  
          // 初始化旧版WebSocket连接
          await this.initWebSocketOld();
  
        } catch (error) {
          console.error("WebSocket连接失败:", error);
          uni.showToast({
            title: "WebSocket连接失败",
            icon: "none",
          });
        }
      },
  
      async initWebSocketOld() {
        const token = uni.getStorageSync('token');
        // 注意：tenantId 在当前 URL 中未使用，但保留以便将来可能需要
  
        // 构建WebSocket URL
        const wsUrl = `${webSocketUrlOld}?access_token=${token}`;
        this.socketOld = new websocketUtil(wsUrl, 10, {}, 'ping');
  
        // 设置消息处理函数
        this.socketOld.getMessage((res) => {
          console.log('收到socketOld消息:', res);
          try {
            // 解析消息数据
            let data;
            if (typeof res.data === 'string' && res.data.trim() !== '') {
              data = JSON.parse(res.data);
            } else if (typeof res.data === 'object') {
              data = res.data;
            } else {
              console.error('无法解析的socketOld消息数据类型:', typeof res.data);
              return;
            }
  
            // 处理返回的数据
            this.handleSocketOldResponse(data);
          } catch (error) {
            console.error('处理socketOld消息失败:', error);
          }
        });
      },
  
      /**
       * 处理socketOld返回的消息
       * @param {Object} data - 返回的数据
       */
      handleSocketOldResponse(data) {
        try {
          // 检查数据是否存在
          if (!data) {
            console.error('返回数据为空');
            return;
          }
  
          // 检查是否有content字段
          if (data.text.msg) {
            // 如果是结束标记
            if (data.text.msg === 'dreamdeck') {
              this.currentChat.msg = ''
            } else if (data.text.msg === '<Finish>') {
              console.log('收到结束标记，处理完整消息:', this.currentChat.msg);
              this.processCompletedMessage();
            } else {
              // 累积消息内容
              this.currentChat.msg += data.text.msg;
              console.log('累积消息内容:', this.currentChat.msg);
            }
          } else {
            console.log('返回数据不包含msg字段:', data);
          }
        } catch (error) {
          console.error('处理socketOld响应失败:', error);
        }
      },
  
      /**
       * 处理完整的消息内容
       */
      processCompletedMessage() {
        try {
          console.log('处理完整消息:', this.currentChat.msg);
  
          // 添加用户消息到消息列表
          this.logList.push({
            id: Date.now().toString(),
            msg: this.currentChat.question,
            timestamp: new Date().getTime(),
            type: 0, // 0表示用户消息
            username: "用户123",
            avatar: uni.getStorageSync('avatar'),
          });
  
          // 判断消息是否包含逗号
          if (this.currentChat.msg.includes(',')) {
            this.currentChat.index = 0;
            console.log('消息包含逗号，分割为数组:', this.currentChat.msg.split(','));
  
            // 将分割后的索引转换为智能体ID
            this.currentChat.singleAgentIds = this.currentChat.msg.split(',').map(i => {
              const index = parseInt(i.trim());
              if (isNaN(index) || index < 0 || index >= this.memberList.length) {
                console.error('无效的索引:', i);
                return this.singleAgentId; // 使用默认智能体ID
              }
              return this.memberList[index].singleAgentId;
            });
  
            // 发送消息
            this.sendChatMessage();
          } else {
            this.currentChat.index = 0;
            const index = parseInt(this.currentChat.msg.trim());
  
            if (isNaN(index) || index < 0 || index >= this.memberList.length) {
              console.error('无效的索引:', this.currentChat.msg);
              this.currentChat.singleAgentIds = [this.singleAgentId]; // 使用默认智能体ID
            } else {
              this.currentChat.singleAgentIds = [this.memberList[index].singleAgentId];
            }
  
            // 发送消息
            this.sendChatMessage();
          }
  
          // 重置消息内容
          this.currentChat.msg = '';
        } catch (error) {
          console.error('处理完整消息失败:', error);
          // 出错时使用默认智能体ID发送消息
          this.currentChat.singleAgentIds = [this.singleAgentId];
          this.sendChatMessage();
          this.currentChat.msg = '';
        }
      },
  
      /**
       * 使用socketOld发送预处理消息
       * @param {String} content - 消息内容
       */
      async sendPreprocessMessage(content) {
        try {
          // 重置累积的消息内容
          this.currentChat.msg = '';
  
          if (!this.socketOld) {
            await this.initWebSocketOld();
          }
  
          // 构建消息格式
          const message = {
            type: 'chat',
            msg: {
              description: content,
              cache: false,
              singleAgentId: this.singleAgentId,
              msgStop: false
            }
          };
  
          console.log('发送预处理消息:', message);
          this.socketOld.send(message);
        } catch (error) {
          console.error('发送预处理消息失败:', error);
          // 如果预处理失败，直接使用默认的智能体ID发送消息
          this.sendDirectMessage(content);
        }
      },
  
      /**
       * 使用匹配到的agentId发送正式消息
       * @param {String} agentId - 智能体ID
       * @param {String} content - 消息内容
       */
      async sendFormalMessage(agentId, content) {
        try {
          // 停止当前播放
          await this.messageService.stopCurrentPlayback();
  
          // 添加用户消息到消息列表
          this.logList.push({
            id: Date.now().toString(),
            msg: content,
            timestamp: new Date().getTime(),
            type: 0, // 0表示用户消息
            username: "用户123",
            avatar: uni.getStorageSync('avatar'),
          });
  
          // 发送消息到服务器
          const success = await this.socket.send({
            role: "user",
            content: content,
            voice: this.chatConfig.voice,
            agent_id: agentId,
            mode: this.chatConfig.mode,
            user_id: this.chatConfig.user_id,
            conversation_id: this.chatConfig.conversation_id
          });
  
          if (success) {
            console.log("正式消息发送成功");
          } else {
            console.error("正式消息发送失败");
            uni.showToast({
              title: "消息发送失败",
              icon: "none",
            });
          }
  
          this.scrollToLower();
        } catch (error) {
          console.error("发送正式消息失败:", error);
          uni.showToast({
            title: "发送消息失败",
            icon: "none",
          });
        }
      },
  
      /**
       * 初始化语音识别 WebSocket 连接
       */
      async initAsrWebSocket() {
        try {
          console.log("初始化 ASR WebSocket 连接");
  
          // 获取token和tenantId
          const token = uni.getStorageSync("token");
          const tenantId = uni.getStorageSync("sysTenantId");
  
          // 构建WebSocket URL
          const wsUrl = `${webSocketUrl}/audio/asr-chat-tts?access_token=${token}&Tenant-Id=${tenantId}`;
  
          // 构建HTTP URL
          const httpUrl = `${webSocketUrl.replace('ws', 'http')}/audio/asr-stream`;
  
          // 构建头信息
          const headers = {
            AuthToken: token,
            Voice: this.chatConfig.voice,
            Mode: this.chatConfig.mode,
            DeviceType: this.chatConfig.deviceType,
            AgentId: this.singleAgentId,
            UserId: this.chatConfig.user_id,
            ConversationId: this.chatConfig.conversation_id,
            SampleRate: "16000",
            StartValue: "4000",
            EndValue: "3000",
            ChunkSize: "1024",
            Role: "BotA"
          };
  
          // 初始化语音识别服务
          const success = await asrChatService.init({
            wsUrl,
            httpUrl,
            headers,
            asrMode: 'websocket',
  
            // 用户语音识别结果回调
            onUserSpeechRecognized: (content) => {
              console.log('用户语音识别结果:', content);
              this.handleVoiceInput(content);
            },
  
            // 机器人回答回调
            onBotResponse: (data) => {
              console.log('机器人回答:', data);
              // this.handleBotResponse(data);
            },
  
            // 对话结束回调
            onFinish: () => {
              console.log('对话结束');
              this.scrollToLower();
            },
  
            // 错误回调
            onError: (error) => {
              console.error('语音识别服务错误:', error);
              uni.showToast({
                title: '语音识别服务错误',
                icon: 'none'
              });
            }
          });
  
          if (success) {
            console.log("语音识别服务初始化成功");
            this.isAsrConnected = true;
          } else {
            console.error("语音识别服务初始化失败");
            this.isAsrConnected = false;
          }
        } catch (error) {
          console.error("ASR WebSocket 连接失败:", error);
          this.isAsrConnected = false;
        }
      },
  
      /**
       * 发送用户消息
       * @param {String} content - 消息内容
       */
      async sendMessage(content) {
        try {
          // 验证输入
          if (!content.trim()) {
            uni.showToast({
              title: "消息不能为空",
              icon: "none"
            });
            return;
          }
  
          // 停止之前的对话
          this.sendStop();
  
          // 重置对话索引
          this.currentChat.singleAgentIds = [this.singleAgentId];
          this.currentChat.index = 0;
          this.currentChat.question = content;
          this.currentChat.msg = '';
  
          // 处理@的智能体
          if (content.includes('@')) {
            this.atStaticList.forEach(item => {
              if (content.includes(`@${item.name}`)) {
                this.currentChat.singleAgentIds.push(item.singleAgentId);
              }
            });
  
            // 如果有@特定智能体，直接使用常规流程发送
            this.sendDirectMessage(content);
          } else {
            // 如果没有@特定智能体，先进行预处理
            await this.sendPreprocessMessage(content);
          }
        } catch (error) {
          console.error("发送消息失败:", error);
          uni.showToast({
            title: "发送消息失败",
            icon: "none",
          });
          // 出错时尝试使用常规方式发送
          this.sendDirectMessage(content);
        }
      },
  
      /**
       * 直接发送消息（不经过预处理）
       * @param {String} content - 消息内容
       */
      async sendDirectMessage(content) {
        try {
          // 如果没有@任何人，默认发送给灵宝
          if (this.currentChat.singleAgentIds.length === 0) {
            this.currentChat.singleAgentIds.push(this.singleAgentId);
          }
  
          // 停止当前播放
          await this.messageService.stopCurrentPlayback();
  
          // 添加用户消息到消息列表
          this.logList.push({
            id: Date.now().toString(),
            msg: content,
            timestamp: new Date().getTime(),
            type: 0, // 0表示用户消息
            username: "用户123",
            avatar: uni.getStorageSync('avatar'),
          });
  
          // 发送消息到服务器
          const success = await this.socket.send({
            role: "user",
            content: content,
            voice: this.chatConfig.voice,
            agent_id: this.currentChat.singleAgentIds[this.currentChat.index],
            mode: this.chatConfig.mode,
            user_id: this.chatConfig.user_id,
            conversation_id: this.chatConfig.conversation_id
          });
  
          if (success) {
            console.log("消息发送成功");
          } else {
            console.error("消息发送失败");
            uni.showToast({
              title: "消息发送失败",
              icon: "none",
            });
          }
  
          this.scrollToLower();
        } catch (error) {
          console.error("直接发送消息失败:", error);
          uni.showToast({
            title: "发送消息失败",
            icon: "none",
          });
        }
      },
  
      /**
       * 监听滚动事件
       * @param {Object} e - 滚动事件对象
       */
      scroll(e) {
        this.scrollHeight = e.target.scrollHeight;
      },
  
      /**
       * 播放或停止语音
       * @param {Number} index - 消息索引
       */
      async playVoice(index) {
        try {
          const currentItem = this.logList[index];
          console.log("点击播放按钮:", currentItem);
  
          // 判断如果没有音频则不播放
          if (
            !currentItem ||
            !currentItem.audio_urls ||
            currentItem.audio_urls.length === 0 ||
            currentItem.voiceTime <= 0
          ) {
            console.log("没有可播放的音频");
            uni.showToast({ title: "没有可播放的音频", icon: "none" });
            return;
          }
  
          // 如果当前项目正在播放，则停止播放
          if (currentItem.voicePlay) {
            console.log("停止当前播放");
            audioManager.stop();
            currentItem.voicePlay = false;
            return;
          }
  
          // 停止所有正在播放的音频并重置状态
          this.resetAllVoicePlay();
  
          // 等待audioManager完全停止并重置状态
          console.log("等待audioManager完全停止并重置状态...");
          await audioManager.stop();
  
          // 设置当前项的播放状态
          currentItem.voicePlay = true;
  
          // 禁用超时处理，防止音频播放被超时中断
          audioManager.setTimeoutEnabled(false);
  
          // 使用循环Promise的方式播放音频，不使用队列
          console.log("开始播放音频，总数:", currentItem.audio_urls.length);
  
          try {
            // 依次播放所有音频
            for (
              let i = 0;
              i < currentItem.audio_urls.length && currentItem.voicePlay;
              i++
            ) {
              const audioPath = currentItem.audio_urls[i];
              console.log(
                `播放音频 ${i + 1}/${currentItem.audio_urls.length}:`,
                audioPath
              );
  
              // 直接播放音频，不使用队列
              await audioManager.playDirectly(audioPath, 1);
  
              // 如果播放过程中停止了，则中断循环
              if (!currentItem.voicePlay) {
                console.log("播放过程中停止了，中断循环");
                break;
              }
            }
  
            // 检查当前项是否仍在播放状态
            if (currentItem.voicePlay) {
              console.log("所有音频播放完成，重置状态");
              currentItem.voicePlay = false;
            }
          } catch (error) {
            console.error("音频播放过程中出错:", error);
            // 重置播放状态
            if (currentItem.voicePlay) {
              currentItem.voicePlay = false;
            }
          } finally {
            // 恢复超时处理的设置
            audioManager.setTimeoutEnabled(true);
          }
        } catch (error) {
          console.error("播放语音失败:", error);
          uni.showToast({ title: "播放语音失败", icon: "none" });
  
          // 重置播放状态
          if (index >= 0 && this.logList[index]) {
            this.logList[index].voicePlay = false;
          }
        }
      },
  
      /**
       * 重置所有音频播放状态
       */
      resetAllVoicePlay() {
        this.logList.forEach(item => {
          if (item.voicePlay) {
            item.voicePlay = false;
          }
        });
      },
  
      /**
       * 获取音频时长
       * @param {String} src - 音频文件路径
       * @returns {Promise<Number>} - 音频时长（秒）
       */
      getAudioDuration(src) {
        let duration = 0;
        return new Promise((resolve) => {
          const audioContext = uni.createInnerAudioContext({
            useWebAudioImplement: true,
          });
          audioContext.src = src;
          audioContext.onCanplay(() => {
            duration = audioContext.duration;
            setTimeout(() => {
              duration = audioContext.duration;
              console.log("音频时长：", duration);
              resolve(duration);
              audioContext.destroy();
            }, 500);
          });
        });
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container {
    width: 100%;
    height: 100vh;
    background-color: #f7f7f7;
    display: flex;
    flex-direction: column;
  
    .background {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
  
      &-round1 {
        width: 152rpx;
        height: 152rpx;
        position: absolute;
        background: #f8b5ce;
        filter: blur(51rpx);
        border-radius: 50%;
        right: 114rpx;
        top: -16rpx;
      }
  
      &-round2 {
        width: 152rpx;
        height: 152rpx;
        position: absolute;
        background: #c8d6f3;
        filter: blur(51rpx);
        border-radius: 50%;
        right: -35rpx;
        top: 175rpx;
      }
  
      &-round3 {
        width: 300rpx;
        height: 300rpx;
        position: absolute;
        background: #c5f5f3;
        filter: blur(51rpx);
        border-radius: 50%;
        left: -132rpx;
        top: 43rpx;
      }
    }
  
    .member-list {
      width: 100%;
      // padding: 0 32rpx;
  
      &-content {
        width: fit-content;
        padding: 0 10rpx 20rpx 10rpx;
        display: flex;
        flex-direction: row;
        align-items: center;
  
        .member-item {
          width: fit-content;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          padding: 10rpx 13rpx;
  
          .avatar {
            width: 160rpx;
            height: 170rpx;
          }
  
          .name {
            width: 140rpx;
            font-size: 24rpx;
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align: center;
            line-height: 34rpx;
          }
  
          .sub-title {
            width: fit-content;
            max-width: 140rpx;
            font-size: 20rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align: center;
            border-radius: 10rpx;
            padding: 4rpx 8rpx;
            margin-top: 8rpx;
          }
        }
      }
    }
  
    .top {
      width: 100%;
      padding: 0 32rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
  
      .group-info {
        display: flex;
        align-items: center;
  
        .group-name {
          color: #fff;
          font-size: 32rpx;
          font-weight: 500;
        }
  
        .member-count {
          color: rgba(255, 255, 255, 0.8);
          font-size: 24rpx;
          margin-left: 8rpx;
        }
      }
    }
  
    .chat-container {
      padding: 20rpx 20rpx 40rpx 20rpx;
      width: calc(100% - 40rpx);
      position: relative;
      overflow: hidden;
  
      .chat-bg {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
      }
  
      &-scroll {
        height: calc(100% - 130px);
      }
  
      .question-container {
        padding: 20rpx 0;
        position: relative;
        width: 100%;
        height: calc(140rpx - 30rpx);
        display: flex;
        flex-wrap: wrap;
        border-top: 1px solid #e5e5e5;
  
        .question-item {
          color: #767676;
          font-size: 24rpx;
          font-weight: 500;
          background: #fff;
          margin-right: 10rpx;
          margin-bottom: 10rpx;
          border-radius: 100rpx;
          padding: 10rpx 16rpx;
        }
  
        .at {
          border-radius: 0px 10px 10px 10px;
          background: #e46661;
          color: #fff;
        }
      }
  
      .message-item {
        padding-bottom: 20rpx;
  
        .timestamp {
          text-align: center;
          font-size: 24rpx;
          color: #999;
          margin: 20rpx 0;
        }
  
        .message-content {
          display: flex;
          align-items: flex-start;
          margin-top: 20rpx;
  
          &.self {
            flex-direction: row-reverse;
  
            .message-bubble {
              border-radius: 10px 0px 10px 10px;
              background: rgba(255, 15, 0, 0.5);
              backdrop-filter: blur(5px);
              margin-right: 16rpx;
              color: #fff;
            }
          }
  
          &.other {
            .message-bubble {
              border-radius: 0px 10px 10px 10px;
              background: rgba(0, 0, 0, 0.5);
              margin-left: 16rpx;
              color: #fff;
              backdrop-filter: blur(5px);
            }
          }
  
          .avatar {
            width: 80rpx;
            height: 86rpx;
            border-radius: 8rpx;
          }
  
          .message-body {
            max-width: 80%;
  
            .username {
              font-size: 24rpx;
              color: #999;
              margin-bottom: 8rpx;
              margin-left: 16rpx;
            }
  
            .message-bubble {
              padding: 20rpx;
              font-size: 28rpx;
              word-break: break-all;
              position: relative;
  
              .voice-control {
                position: absolute;
                top: -42rpx;
                left: 64rpx;
                background: #45b18b;
                border-radius: 10px;
                padding: 6rpx 16rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8rpx;
                white-space: nowrap;
                color: #fff;
                font-size: 24rpx;
                z-index: 2;
  
                view {
                  display: inline-block;
                }
              }
            }
          }
        }
      }
  
      .chat-sender {
        ::v-deep .container {
          padding: 0;
        }
      }
    }
  
    ::v-deep .u-popup {
      .at-popup {
        position: relative;
        width: 100%;
        height: 68vh;
  
        .at-top {
          width: 100%;
          position: absolute;
          top: -390rpx;
          left: 0;
          z-index: 2;
        }
  
        .at-content {
          width: calc(100% - 40rpx);
          height: calc(100% - 20rpx);
          background-color: #fff;
          border-radius: 20rpx;
          padding: 80rpx 20rpx 0 20rpx;
          color: #3d3d3d;
  
          &-header {
            height: 70rpx;
            position: relative;
            z-index: 3;
            display: flex;
            align-items: center;
            justify-content: space-between;
  
            &-text {
              display: flex;
              align-items: center;
              justify-content: center;
            }
  
            .at-content-header-title {
              font-size: 32rpx;
              font-weight: 500;
            }
  
            .at-content-header-right {
              display: flex;
              align-items: center;
              justify-content: space-between;
              font-size: 32rpx;
              width: 118rpx;
  
              .is-finish {
                color: #fff;
                border-radius: 10rpx;
                background: #FA518D;
                padding: 4rpx 8rpx;
              }
            }
          }
  
          .at-content-body {
            height: calc(100% - 70rpx);
  
            &-scroll {
              width: 100%;
              height: 100%;
  
              &-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 20rpx 0;
                border-bottom: 1px solid #EDEDED;
  
                .left {
                  display: flex;
                  align-items: center;
  
                  .avatar {
                    width: 84rpx;
                    height: 90rpx;
                  }
  
                  .name {
                    font-size: 28rpx;
                    font-weight: 500;
                    margin-left: 16rpx;
                  }
                }
  
              }
            }
  
          }
  
          .choose-btn {
            width: 42rpx;
            height: 42rpx;
            background: #f5f5f5;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            transition: all 0.5s;
  
            &::after {
              content: "";
              width: 60%;
              height: 60%;
              background: #fff;
              border-radius: 50%;
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              z-index: 2;
            }
          }
  
          .is-at {
            background-color: #FA518D;
          }
  
          .is-all {
            background-color: #FA518D;
          }
        }
      }
    }
  }
  </style>
  