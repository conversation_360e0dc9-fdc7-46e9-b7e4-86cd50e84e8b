<template>
  <div class="dd_map">
    <div id="dd_map_container"></div>
    <!-- 分类区 -->
    <div class="dd_map_tabbar">
      <!-- 赏花分类区模板 -->
      <div v-if="themId == 'saas_theme_4'" class="catory_outer">
        <div class="catory_item" v-for="(item, index) in poiTypeList" @click="tabTypePoi(item)" :key="index" :style="poiParams.typeId == item.id
          ? data.categoryActiveStyle
          : data.categoryInactiveStyle
          ">
          {{ item.name }}
        </div>
        <!-- 全部 -->
        <div class="catory_item" :style="poiParams.typeId == ''
          ? data.categoryActiveStyle
          : data.categoryInactiveStyle
          " @click="tabMarkerAll()">
          全部
        </div>
      </div>
      <!-- 常规版本 -->
      <!-- 类型切换 -->
      <div v-else :class="themId == 'saas_theme_2'
        ? 'dd_map_tab_type dd_map_tab_type_dyh'
        : themId == 'saas_theme_1'
          ? 'dd_map_tab_type dd_map_tab_type_dragon'
          : 'dd_map_tab_type'
        ">
        <div class="dd_map_tab_type_back">
          <div></div>
        </div>
        <div class="dd_map_tab_type_absolute">
          <div class="dd_map_tab_type_flex" @touchstart="tabTouchstart" @touchmove="tabTouchmove"
            @touchend="tabTouchend">
            <div v-for="(item, index) in poiTypeList" :key="index" class="dd_map_tab_type_flex_list" :class="poiParams.typeId == item.id
              ? themId == 'saas_theme_2'
                ? 'dd_map_tab_type_flex_list_opacity_dyh'
                : themId == 'saas_theme_1'
                  ? 'dd_map_tab_type_flex_list_opacity_lt'
                  : 'dd_map_tab_type_flex_list_opacity_lt'
              : ''
              " @click="tabTypePoi(item)">
              <div class="dd_map_tab_type_flex_list_img">
                <img :src="item.icon" alt />
              </div>
              <p :class="themId == 'saas_theme_2'
                ? 'dd_map_tab_type_flex_list_dyh_p'
                : ''
                ">
                {{ item.name }}
              </p>
            </div>
          </div>
          <div class="dd_map_tab_type_flex_list" :class="poiParams.typeId == ''
            ? themId == 'saas_theme_2'
              ? 'dd_map_tab_type_flex_list_opacity_dyh'
              : themId == 'saas_theme_1'
                ? 'dd_map_tab_type_flex_list_opacity_lt'
                : 'dd_map_tab_type_flex_list_opacity_lt'
            : ''
            " @click="tabMarkerAll()">
            <div class="dd_map_tab_type_flex_list_img">
              <img v-if="themId == 'saas_theme_2'" src="https://www.dreamdeck.cn:10443/app/icons/sass/map/dyh/all.png"
                alt />
              <img v-else-if="themId == 'saas_theme_1'" src="https://www.dreamdeck.cn:10443/app/icons/sass/ltall.png"
                alt />
              <img v-else src="https://www.dreamdeck.cn:10443/app/icons/sass/lxall.png" alt />
            </div>
            <p :class="themId == 'saas_theme_2'
              ? 'dd_map_tab_type_flex_list_dyh_p'
              : ''
              ">
              全部
            </p>
          </div>
        </div>
      </div>
    </div>
    <!-- 赏花定制月份 -->
    <div class="month" v-if="themId == 'saas_theme_4'">
      <div class="month_item" v-for="(item, index) in monthList" :key="index" @click="monthChange(item, index)" :style="pioTypeListParams.month == item || selectMonthIndex === index
        ? data.monthActiveStyle
        : data.monthInactiveStyle
        ">
        {{ index === 0 ? item : item + "月" }}
      </div>
    </div>
    <!-- 花类配置 -->
    <div class="flower_outer" v-if="themId == 'saas_theme_4'">
      <div v-for="(item, index) in preferredList" :key="index">
        <div class="flower" @click="() => preferClick(index)">
          <div v-show="item.openStatus == '0'" class="prefer">荐</div>
          <image :src="`${baseUrl}${item.icon}`" :width="pioTypeListParams.flowerId.length == 1 &&
            pioTypeListParams.flowerId.includes(item.id)
            ? '35px'
            : '25px'
            " :height="pioTypeListParams.flowerId.length == 1 &&
              pioTypeListParams.flowerId.includes(item.id)
              ? '35px'
              : '25px'
              " class="image"></image>
          <div :style="{
            'font-size': '9px',
            color: '#9F4444',
            'margin-top': '3px',
            'text-align': 'center',
          }">
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
    <div class="dd_map_tab_type_notice" v-if="noticeType">
      <div>
        <img src="https://www.dreamdeck.cn:10443/app/icons/sass/map/notice.png" alt srcset />
        <!-- <div class="dd_map_tab_type_notice_scroll">

          <div>
            {{ announcementList.length ? announcementList[0].title : "" }}
          </div>
        </div> -->
        <el-carousel indicator-position="none" :interval="5000">
          <el-carousel-item v-for="(item, index) in announcementList" :key="index">
            <div @click="clickNotice(item)">{{ item.title }}</div>
          </el-carousel-item>
        </el-carousel>
      </div>
      <!-- <div > -->
      <img @click="noticeType = false" class="dd_map_tab_type_notice_close"
        src="https://www.dreamdeck.cn:10443/app/icons/sass/map/close.png" />
      <!-- </div> -->
    </div>
    <!-- 赏花日历图标 -->
    <div class="flowercalendar" @click="flowercalendarHandler" v-if="themId == 'saas_theme_4'">
      <img class="nav-icon-img" src="https://www.dreamdeck.cn:10443/app/icons/flower/date.png" mode="aspectFill"
        width="64px" height="68px" />
    </div>
    <!-- 底部poi详情 -->
    <div class="dd_map_particulars" v-if="dotType">
      <div v-if="particularsData.isConf == '0'">
        <div class="dd_map_particulars_top" @click="getCommercial">
          <div class="dd_map_particulars_top_one">
            <div class="dd_map_particulars_poi">
              <img :src="particularsData.coverUrl" alt />
              <div class="dd_map_particulars_poi_text">
                <p>{{ particularsData.name ? particularsData.name : "" }}</p>
                <p v-if="particularsData.content" v-html="particularsData.content.substr(0, 30)"></p>
              </div>
            </div>
            <div class="dd_map_particulars_address">
              <img src="https://www.dreamdeck.cn:10443/app/icons/southtwoloop/saas_location.png" alt />
              <p>
                {{ particularsData.address ? particularsData.address : "" }}
              </p>
            </div>
          </div>
          <div class="dd_map_particulars_audio" v-if="particularsData.music">
            <img v-if="audioType == false"
              src="https://www.dreamdeck.cn:10443/app/icons/southtwoloop/saas_audiostart.png" alt
              @click.stop="getAudio" />
            <img v-if="audioType == true" src="https://www.dreamdeck.cn:10443/app/icons/southtwoloop/saas_audioend.png"
              alt @click.stop="getPause" />
          </div>
        </div>
        <div class="dd_map_particulars_flex">
          <div v-if="particularsData.ar">
            <img src="https://www.dreamdeck.cn:10443/app/icons/southtwoloop/saas_ar.png" alt />
            <p>AR</p>
          </div>
          <div @click="getCommercial">
            <img src="https://www.dreamdeck.cn:10443/app/icons/southtwoloop/saas_particulars.png" alt />
            <p>详情</p>
          </div>
          <div @click="openLocation">
            <img src="https://www.dreamdeck.cn:10443/app/icons/southtwoloop/saas_navigation.png" alt />
            <p>导航</p>
          </div>
        </div>
      </div>
      <div class="dd_map_particulars_facility" v-else>
        <p @click="openLocation">导航去这里</p>
        <p v-if="particularsData.tel" @click="callPhone(particularsData.tel)">
          {{ particularsData.tel ? particularsData.tel : "" }}
        </p>
      </div>
    </div>
    <!-- ar -->
    <div class="dd_map_ar" v-if="arType">
      <img :src="particularsData.arImgSrc" alt />
      <img src="https://www.bjdyhsy.com:10443/miniimage/answerclonepopup.png" @click.stop="arType = false" alt />
    </div>
    <div :class="contentTop > 50
      ? 'dd_map_right_position'
      : 'dd_map_right_position dd_map_right_position_all'
      ">
      <!-- 搜索功能 -->
      <div class="dd_map_wire dd_map_synopsis" @click="getSearch">
        <img src="https://www.dreamdeck.cn:10443/app/icons/southtwoloop/saas_search.png" alt />
        <!-- <p>简介</p> -->
      </div>
      <!-- 音频启用按钮 -->
      <div class="dd_map_wire dd_map_audio" @click="enableAudio" v-if="showAudioButton" :title="audioButtonTitle">
        <div class="audio-icon">🔊</div>
        <div class="audio-tip" v-if="isWeChat">微信</div>
      </div>
      <!-- 定位功能 -->
      <div class="dd_map_wire dd_map_location" @click="getMyLocation">
        <img src="https://www.dreamdeck.cn:10443/app/icons/southtwoloop/saas_orientation.png" alt />
      </div>
    </div>
    <!-- 可拖拽dom -->
    <div :class="themId == 'saas_theme_2'
      ? 'dd_map_content_dyh'
      : themId == 'saas_theme_1'
        ? 'dd_map_content_lt'
        : themId == 'saas_theme_2'
          ? 'dd_map_content_lx'
          : themId == 'saas_theme_4'
            ? 'dd_map_content_flower  dd_map_content'
            : 'dd_map_content'
      " ref="mapContent" v-if="!dotType && !show && !isGreenPark" :style="{
        top:
          themId == 'saas_theme_5' ||
            themId == 'saas_theme_6' ||
            themId == 'saas_theme_7' ||
            themId == 'saas_theme_8'
            ? '100%'
            : contentTop + '%',
      }" @touchstart="touchstart" @touchmove="touchmove" @touchend="touchend">
      <!-- 赏花模板活动--没有金刚区 v-if="themId == 'saas_theme4'"-->
      <div class="activity_outer" v-if="themId == 'saas_theme_4'">
        <div v-for="(item, index) in activityList" :key="index" @click.stop="() => navActivity(item)"
          class="activity_item">
          <img :src="`${baseUrl}/admin/file/${item.coverImg}`" class="dd-drawer-content-item-img" width="100%"
            height="100px" mode="aspectFill" />
          <div class="dd-drawer-content-item-title">{{ item.name }}</div>
          <div class="dd-drawer-content-item-info">
            <div class="tag-list">
              <div v-for="(tag, i) in item.label ? item.label.split(',') : []" :key="i">
                {{ tag }}
              </div>
            </div>
            <div class="zan" @click.stop="handleAddCount(item)">
              <img src="https://www.dreamdeck.cn:10443/app/icons/flower/zan.png" class="zan-icon" width="10px"
                height="10px" mode="aspectFill" />
              <div>{{ item.clickNum || 0 }}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 底部金刚区 -->
      <div :class="moduleTreeList.length > 5
        ? 'dd_map_content_type_auto'
        : 'dd_map_content_type'
        " v-if="themId != 'saas_theme_5' &&
          themId != 'saas_theme_6' &&
          themId != 'saas_theme_7' &&
          themId != 'saas_theme_8' &&
          themId != 'saas_theme_4'
        ">
        <div class="dd_map_content_type_list" :class="themId == 'saas_theme_2' ? 'dd_map_content_type_list_dyh' : ''
          " v-for="(item, index) in moduleTreeList" @click="getTicket(item)" :key="index">
          <img :src="item.icon" alt />
          <p>{{ item.name }}</p>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="dd_map_floor" :class="themId == 'saas_theme_2' ? 'dd_map_floor_dyh' : ''">
      <!-- 首页 -->
      <div v-if="themId == 'saas_theme_5' ||
        themId == 'saas_theme_6' ||
        themId == 'saas_theme_7' ||
        themId == 'saas_theme_8'
      " @click="getHome">
        <img v-if="themId == 'saas_theme_6'" src="https://www.bjdyhsy.com:10443/miniimage/navhomeo.png" alt />
        <img v-else src="https://www.dreamdeck.cn:10443/app/icons/sass/home.png" alt />
        <p>首页</p>
      </div>
      <div :class="themId == 'saas_theme_2' || themId == 'saas_theme_6'
        ? 'dd_map_floor_select_dyh'
        : 'dd_map_floor_select'
        ">
        <img v-if="themId == 'saas_theme_2' || themId == 'saas_theme_6'"
          src="https://www.dreamdeck.cn:10443/app/icons/sass/map/dyh/selectNav.png" alt />
        <img v-else-if="themId == 'saas_theme_4'" src="https://www.dreamdeck.cn:10443/app/icons/sass/map/flowerNav.png"
          alt />

        <img v-else src="https://www.dreamdeck.cn:10443/app/icons/sass/lthome.png" alt />
        <p>导览</p>
      </div>
      <div class @click="getOrder" style="transform: translateX(-10px)">
        <img src="https://www.dreamdeck.cn:10443/app/icons/sass/map/flowerOrder.png" v-if="themId == 'saas_theme_4'"
          alt />
        <img src="https://www.dreamdeck.cn:10443/app/icons/southtwoloop/saas_tabbar_order.png" alt v-else />
        <p>订单</p>
      </div>
      <div class @click="getOneself">
        <img v-if="themId == 'saas_theme_4'" src="https://www.dreamdeck.cn:10443/app/icons/sass/map/flowemine.png"
          alt />
        <img v-else src="https://www.dreamdeck.cn:10443/app/icons/southtwoloop/saas_tabbar_personage.png" alt />
        <p>我的</p>
      </div>
    </div>
    <div class="messageNoctice" v-if="messageNocticeShow">
      {{ messageNoctice }}
    </div>
    <!-- 弹框 -->
    <div class="dd_map_popup" v-if="popupType">
      <div>请在园区内进行导航</div>
    </div>
    <!-- 三方导航软件选择 -->
    <div v-if="show" class="navSoft_outer">
      <div>
        <div class="dd_map_item" v-for="(item, index) in sheetList" :key="index" @click="handleMap(item)">
          {{ item.name }}
        </div>
      </div>
    </div>
    <chat v-if="sysTenantId == '11048' || sysTenantId == '10205'" @send="chatSend" :msg="userChatMsg"></chat>
    <div id="demoCanvas" ref="demoCanvas" style="
        width: 100vw;
        height: 100vh;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 9999;
      "></div>
  </div>
</template>

<script>
import {
  getPoiTypeAll,
  geoIndexAll,
  getKongAll,
  getAnnouncementAll,
  getPreferredList,
  getactivityAll,
} from "@/api/operationmini";
import { getByTenantId, getThemeList } from "@/api/operationbase";
import { MAP_KEY } from "@/utils/config";
import MapLoader from "@/utils/amap.js";
import createMarker from "@/utils/marker";
import SVGA from "svgaplayerweb";
// import ChatMixin from "@/mixins/chat.js";
// import ChatMixin from "@/mixins/chat2.js";
import ChatMixin from "@/mixins/chat3.js";
import Chat from "@/components/chat/chat.vue";
export default {
  mixins: [ChatMixin],
  data() {
    return {
      isAudioShow: true, // 录音是否出现
      themId: "", // 主题色id
      tMap: null, // 腾讯地图实例
      TXMap: null, // 腾讯地图原型
      markerList: [],
      markerLayer: [],
      geolocation: "",
      sheetList: [
        {
          name: "百度地图",
          id: 1,
        },
        {
          name: "高德地图",
          id: 2,
        },
        {
          name: "腾讯地图",
          id: 3,
        },
      ],
      mapData: {
        zoom: "",
        latitude: "",
        longitude: "",
        miniStyleMap: "",
      },
      tenantData: {},
      poiParams: {
        hierarchy: "",
        pointLeft: "",
        pointRight: "",
        typeId: "",
        sysTenantId: "",
        firstIds: '2,3'
      },
      dotType: false,
      particularsData: {},
      poiTypeList: [],
      contentTop: "79",
      offsetHeightVh: "",
      touchY: "",
      touchTypeY: "",
      slideType: true,
      moduleTreeList: [],
      currentIndex: 0,
      markerClickType: false,
      locationData: {},
      polylineLayer: "",
      selectMarkerId: "",
      sysTenantId: "",
      arType: false,
      audioData: "",
      audioType: false,
      popupType: false,
      noticeType: true,
      announcementList: [],
      themList: [], // 主题风格
      messageNoctice: "",
      messageNocticeShow: false,
      data: {},
      monthList: ["全部", 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
      selectMonthIndex: "",
      pioTypeListParams: {
        month: new Date().getMonth() + 1,
        flowerId: [],
      },
      preferredList: [] /* 花类数据 */,
      activityList: [] /* 活动列表 */,
      baseUrl: process.env.VUE_APP_BASE_API,
      show: false /* 三方导航软件选择 */,
      userChatMsg: '',
      isGreenPark: false,
      showAudioButton: false /* 是否显示音频启用按钮 */,
      isWeChat: false /* 是否是微信环境 */
    };
  },
  computed: {
    audioButtonTitle() {
      return this.isWeChat ? '点击启用微信音频自动播放' : '点击启用音频自动播放';
    }
  },
  components: { Chat },
  created() {
    this.getThemeList();

  },
  mounted() {
    this.data = {
      categoryActiveStyle: {
        color: "#fff",
        fontWeight: "bold",
        "line-height": "34px",
        "background-image": ` url("https://www.dreamdeck.cn:10443/app/icons/flower/active.png")`,
        "background-position": "0 0",
        "background-size": "100% 100%",
        "background-repeat": "no-repeat",
        padding: "0 15px",
        "font-size": " 12px",
        overflow: "auto",
        "text-align": "center",
        "margin-right": "10px",
      },
      categoryInactiveStyle: {
        color: "#9F4444",
        "background-image": `url("https://www.dreamdeck.cn:10443/app/icons/flower/inactive.png")`,
        "background-position": "0 0",
        "background-size": "100% 100%",
        "background-repeat": "no-repeat",
        padding: "0 15px",
        "font-size": " 12px",
        "line-height": "34px",
        overflow: "auto",
        "text-align": "center",
        "margin-right": "10px",
      },
      monthActiveStyle: {
        color: "#fff",
        fontWeight: "bold",
        "background-image": `url("https://www.dreamdeck.cn:10443/app/icons/flower/month-active.png")`,
      },
      monthInactiveStyle: {
        color: "#FF9595",
        "background-image": `url("https://www.dreamdeck.cn:10443/app/icons/flower/month-inactive.png")`,
      },
      backIcon: "https://www.dreamdeck.cn:10443/app/icons/flower/page-back.png",
      "background-color": "rgb(255, 149, 149)",
    };
    let height =
      (document.body.offsetHeight - 70 - 110) / document.body.offsetHeight;
    this.offsetHeightVh = this.contentTop = height.toFixed(2) * 100;
    this.sysTenantId = window.localStorage.getItem("sysTenantId");
    if (this.sysTenantId == '11048' || this.sysTenantId == '10205') {
      this.isGreenPark = true;
    }

    // 检查是否是移动设备，如果是则显示音频启用按钮
    this.checkAudioButtonVisibility();

    this.initMap();

    this.getKongAll();
    // this.getAnnouncementAll();
  },
  methods: {
    /**
     * 检查音频按钮显示状态
     */
    checkAudioButtonVisibility() {
      // 检查是否是移动设备
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      // 检查是否是微信环境
      this.isWeChat = /MicroMessenger/i.test(navigator.userAgent);

      // 检查是否已经启用过音频
      const audioEnabled = localStorage.getItem('audioEnabled') === 'true';

      // 在移动设备或微信环境且未启用音频时显示按钮
      this.showAudioButton = (isMobile || this.isWeChat) && !audioEnabled;

      // 如果是微信环境，显示特殊提示
      if (this.isWeChat && !audioEnabled) {
        console.log('检测到微信环境，将使用微信专用音频解锁方案');
      }

      // 监听音频解锁事件
      window.addEventListener('audioUnlocked', () => {
        this.showAudioButton = false;
        localStorage.setItem('audioEnabled', 'true');
      });
    },

    /**
     * 启用音频播放
     */
    async enableAudio() {
      try {
        console.log('用户点击启用音频按钮');

        // 检查是否是微信环境
        const isWeChat = /MicroMessenger/i.test(navigator.userAgent);

        if (isWeChat) {
          // 微信环境专用解锁方案
          await this.enableWeChatAudio();
        } else {
          // 普通移动端解锁方案
          await this.enableMobileAudio();
        }

        // 触发自定义事件
        window.dispatchEvent(new CustomEvent('audioUnlocked'));

        // 隐藏按钮
        this.showAudioButton = false;
        localStorage.setItem('audioEnabled', 'true');

        // 显示成功提示
        this.$message({
          type: 'success',
          message: '音频播放已启用！现在智能体语音会自动播放',
          duration: 2000
        });

        console.log('音频播放已完全启用');
      } catch (error) {
        console.error('启用音频失败:', error);
        this.$message({
          type: 'warning',
          message: '音频启用失败，请尝试刷新页面后重试',
          duration: 3000
        });
      }
    },

    /**
     * 微信环境专用音频启用方案
     */
    async enableWeChatAudio() {
      console.log('使用微信专用音频解锁方案');

      // 方法1: 使用微信JSSDK播放音频
      if (this.$wx && this.$wx.config) {
        try {
          // 确保微信JSSDK已经配置
          console.log('尝试使用微信JSSDK解锁音频');

          // 创建一个极短的音频文件
          const shortAudio = new Audio();
          shortAudio.src = "data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA";
          shortAudio.volume = 0.01;
          shortAudio.preload = 'auto';

          // 在微信中，需要在用户交互事件中立即播放
          await shortAudio.play();
          console.log('微信环境音频解锁成功');

        } catch (error) {
          console.warn('微信JSSDK音频解锁失败，尝试其他方法:', error);
        }
      }

      // 方法2: 创建多个音频元素并尝试播放
      const audioPromises = [];
      for (let i = 0; i < 5; i++) {
        const audio = new Audio();
        audio.src = "data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA";
        audio.volume = 0.01;
        audio.preload = 'auto';

        const promise = audio.play().then(() => {
          console.log(`微信音频元素 ${i} 解锁成功`);
          audio.pause();
          audio.currentTime = 0;
          return audio;
        }).catch(err => {
          console.warn(`微信音频元素 ${i} 解锁失败:`, err);
          return null;
        });

        audioPromises.push(promise);
      }

      // 等待所有音频元素解锁完成
      const unlockedAudios = await Promise.all(audioPromises);
      const successfulAudios = unlockedAudios.filter(audio => audio !== null);

      console.log(`微信环境成功解锁 ${successfulAudios.length} 个音频元素`);

      // 方法3: 创建微信专用音频播放池
      this.createWeChatAudioPool(successfulAudios);

      // 方法4: 通知audioManager
      if (window.audioManager) {
        window.audioManager.autoplaySupport = {
          userInteractionRequired: false,
          canAutoplay: true,
          isWeChat: true
        };
        console.log('微信环境audioManager状态已更新');
      }
    },

    /**
     * 普通移动端音频启用方案
     */
    async enableMobileAudio() {
      console.log('使用普通移动端音频解锁方案');

      // 方法1: 创建并播放静音音频
      const silentAudio = new Audio();
      silentAudio.src = "data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA";
      silentAudio.volume = 0.01;
      silentAudio.muted = false;

      await silentAudio.play();
      console.log('静音音频播放成功');

      // 方法2: 如果有AudioContext，也要解锁它
      if (window.AudioContext) {
        try {
          if (!window.globalAudioContext) {
            window.globalAudioContext = new AudioContext();
          }

          if (window.globalAudioContext.state === 'suspended') {
            await window.globalAudioContext.resume();
            console.log('AudioContext已恢复');
          }
        } catch (error) {
          console.warn('AudioContext处理失败:', error);
        }
      }

      // 方法3: 通知audioManager音频已解锁
      if (window.audioManager) {
        window.audioManager.autoplaySupport = {
          userInteractionRequired: false,
          canAutoplay: true
        };
        console.log('audioManager状态已更新');
      }

      // 方法4: 创建音频播放池
      this.createAudioPool();
    },

    /**
     * 创建音频播放池
     */
    createAudioPool() {
      try {
        // 创建全局音频播放池
        if (!window.audioPool) {
          window.audioPool = [];
        }

        // 预先创建几个音频元素并播放静音音频来解锁
        for (let i = 0; i < 3; i++) {
          const audio = new Audio();
          audio.src = "data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA";
          audio.volume = 0.01;
          audio.preload = 'auto';

          // 尝试播放来解锁这个音频元素
          audio.play().then(() => {
            console.log(`音频池元素 ${i} 已解锁`);
            audio.pause();
            audio.currentTime = 0;
            window.audioPool.push(audio);
          }).catch(err => {
            console.warn(`音频池元素 ${i} 解锁失败:`, err);
          });
        }

        // 创建一个全局的音频播放函数
        window.playAudioFromPool = (src, volume = 1) => {
          return new Promise((resolve, reject) => {
            if (!window.audioPool || window.audioPool.length === 0) {
              reject(new Error('音频池未初始化'));
              return;
            }

            // 从池中获取一个音频元素
            const audio = window.audioPool.shift();
            audio.src = src;
            audio.volume = volume;

            const cleanup = () => {
              // 播放完成后重置并放回池中
              audio.pause();
              audio.currentTime = 0;
              audio.src = "data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA";
              window.audioPool.push(audio);
            };

            audio.onended = () => {
              cleanup();
              resolve();
            };

            audio.onerror = (err) => {
              cleanup();
              reject(err);
            };

            // 播放音频
            audio.play().then(() => {
              console.log('从音频池播放音频成功:', src);
            }).catch(err => {
              cleanup();
              reject(err);
            });
          });
        };

        console.log('音频播放池已创建');
      } catch (error) {
        console.error('创建音频播放池失败:', error);
      }
    },

    /**
     * 创建微信专用音频播放池
     */
    createWeChatAudioPool(preUnlockedAudios = []) {
      try {
        console.log('创建微信专用音频播放池');

        // 创建全局微信音频播放池
        if (!window.weChatAudioPool) {
          window.weChatAudioPool = [];
        }

        // 添加预解锁的音频元素
        if (preUnlockedAudios.length > 0) {
          window.weChatAudioPool.push(...preUnlockedAudios);
          console.log(`添加了 ${preUnlockedAudios.length} 个预解锁的音频元素`);
        }

        // 创建微信专用的音频播放函数
        window.playWeChatAudio = (src, volume = 1) => {
          return new Promise((resolve, reject) => {
            console.log('使用微信音频池播放:', src);

            // 优先使用池中的音频元素
            let audio;
            if (window.weChatAudioPool && window.weChatAudioPool.length > 0) {
              audio = window.weChatAudioPool.shift();
              console.log('从微信音频池获取音频元素');
            } else {
              // 如果池中没有可用元素，创建新的
              audio = new Audio();
              console.log('创建新的微信音频元素');
            }

            audio.src = src;
            audio.volume = volume;
            audio.preload = 'auto';

            const cleanup = () => {
              // 播放完成后重置并放回池中
              audio.pause();
              audio.currentTime = 0;
              audio.src = "data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA";
              if (window.weChatAudioPool) {
                window.weChatAudioPool.push(audio);
              }
            };

            audio.onended = () => {
              cleanup();
              resolve();
            };

            audio.onerror = (err) => {
              cleanup();
              reject(err);
            };

            // 在微信中立即播放
            audio.play().then(() => {
              console.log('微信音频播放成功:', src);
            }).catch(err => {
              console.error('微信音频播放失败:', err);
              cleanup();
              reject(err);
            });
          });
        };

        console.log('微信专用音频播放池已创建');
      } catch (error) {
        console.error('创建微信音频播放池失败:', error);
      }
    },

    closeAudio(data) {
      // console.log('录音数据',data);
      this.isShow = false;
      // 处理其他逻辑......
    },
    initMachineSVGA() {
      let player = new SVGA.Player("#demoCanvas");
      let parser = new SVGA.Parser("#demoCanvas");
      let w = window.innerWidth; //拿页面宽
      let h = window.innerHeight; //拿页面高
      // 必须是服务器地址或者是线上的地址本地是不可以的 会报错
      parser.load(
        "https://www.dreamdeck.cn:10443/app/icons/operation/project/lihu/svga/yun.svga",
        function (videoItem) {
          videoItem.videoSize.width = w; //给svga图片宽赋值
          videoItem.videoSize.height = h; //给svga图片高赋值
          // console.log(player);
          // console.log(videoItem);
          player.setVideoItem(videoItem);
          player.loops = 1;
          player.duration = 5000;
          player.startAnimation();
        }
      );
      player.onFinished(() => {
        document.getElementById("demoCanvas").style.display = "none";
      });
    },
    getThemeList() {
      let data = {
        sysTenantId: 1,
        dictId: 20,
      };
      getThemeList(data).then((res) => {
        // console.log(res, "##########3");
        this.themList = res.data;
      });
    },
    getPreferredList() {
      const { typeId, month } = this.pioTypeListParams;
      getPreferredList({ typeId, month }).then((res) => {
        this.preferredList = res.data ? res.data : [];
        if (this.preferredList.length > 0) {
          this.pioTypeListParams.flowerId = this.preferredList.map((item) => {
            return item.id;
          });
        } else {
          this.pioTypeListParams.flowerId = [];
        }
      });
    },
    getactivityList() {
      getactivityAll({
        type: "1",
        status: 0,
        sysTenantId: window.localStorage.getItem("sysTenantId"),
      }).then((res) => {
        this.activityList = this.sortFun(res.data, "sort", "desc");
      });
    },
    // 活动排序
    sortFun(arr, name, type) {
      const compare = (prop) => {
        return (obj1, obj2) => {
          let val1 = obj1[prop];
          let val2 = obj2[prop];
          if (val1 < val2) {
            return -1;
          } else if (val1 > val2) {
            return 1;
          } else {
            return 0;
          }
        };
      };
      if (type == "desc") {
        return arr.sort(compare(name));
      } else {
        return arr.sort(compare(name)).reverse();
      }
    },
    monthChange(item, index) {
      this.selectMonthIndex = index;
      // 全部直接不传月份字段
      if (index == 0) {
        delete this.pioTypeListParams.month;
        // pioTypeListParams.month.value=''
      } else {
        this.pioTypeListParams.month = index;
      }
      if (this.preferredList.length > 0) {
        this.pioTypeListParams.flowerId = this.preferredList.map((item) => {
          return item.id;
        });
      } else {
        this.pioTypeListParams.flowerId = [];
      }
      this.getPreferredList();
    },
    // 赏花日历跳转
    flowercalendarHandler() {
      this.$wx.miniProgram.navigateTo({
        url: "/pages_saas/themeTemplate/flowerTheme/flowercalendar/calendar",
        success: (res) => {
          this.handlePageHide()
        },
      });
    },
    preferClick(index) {
      if (this.pioTypeListParams.flowerId.length > 1) {
        this.pioTypeListParams.flowerId = [preferredList.value[index].id];
      } else {
        if (
          this.pioTypeListParams.flowerId[0] ==
          this.preferredList.value[index].id
        ) {
          this.pioTypeListParams.flowerId = this.preferredList.value.map(
            (item) => {
              return item.id;
            }
          );
        } else {
          this.pioTypeListParams.flowerId = [
            this.preferredList.value[index].id,
          ];
        }
      }
    },
    // 系统公告
    getAnnouncementAll() {
      let data = {
        status: 0,
        sysTenantId: window.localStorage.getItem("sysTenantId"),
      };
      getAnnouncementAll(data).then((res) => {
        // console.log(res, "园区公告-----------");
        if (res.data.length > 0) {
          this.announcementList = res.data;
          if (res.data.length <= 2) {
            this.announcementList = [...this.announcementList, ...res.data];
          }
          if (
            this.themId == "saas_theme_1" ||
            this.themId == "saas_theme_2" ||
            this.themId == "saas_theme_3"
          ) {
            // this.noticeType = true;
          } else {
            this.noticeType = false;
          }
        } else {
          this.noticeType = false;
        }
        // console.log(this.noticeType, "是否展示园区公告");
      });
    },
    // 点击系统公告
    clickNotice(item) {
      // console.log(item, "#########");
      if (item.type == 1 && item.content) {
        this.$wx.miniProgram.navigateTo({
          url: `/pages_banner/detail/detail?id=${item.id}`,
          success: (res) => {
            this.handlePageHide()
          },
        });
      }
    },
    // 音频播放
    getAudio() {
      if (this.audioData) {
        this.audioType = true;
        this.audioData.play();
      }
    },
    // 暂停
    getPause() {
      if (this.audioData) {
        this.audioData.pause();
        this.audioType = false;
      }
    },
    // 类型切换-全部
    tabMarkerAll() {
      this.removeMarder();
      this.dotType = false;
      this.selectMarkerId = "";
      this.poiParams.typeId = "";
      this.geoIndexAll();
    },
    // 三方导航
    openLocation() {
      // this.show = true;
      // this.dotType = false;
      this.handleMap();
    },
    jump(id) {
      const u = navigator.userAgent;
      let url;
      // 判断设备是iOS还是Android，Linux
      const isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
      const isAndroid = u.indexOf("Android") > -1 || u.indexOf("Linux") > -1;
      let lat = this.tenantData.latitude;
      let lon = this.tenantData.longitude;
      let locationName = this.tenantData.address;
      //appname不用修改，用在这个就好 经纬度是目标点的，跳转过去后可以导航到这个目标点
      if (isiOS) {
        // 百度
        if (id == 1) {
        }
        // 高德
        else if (id == 2) {
          url =
            "iosamap://viewMap?sourceApplication=appname&poiname=" +
            locationName +
            "&lat=" +
            lat +
            "&lon=" +
            lon +
            "&dev=0";
        }
        // 腾讯
        else if (id == 3) {
        }

        window.location.href = url;
      }
      if (isAndroid) {
        // 百度
        if (id == 1) {
        }
        // 高德
        else if (id == 2) {
          // url =
          //   "androidamap://viewMap?sourceApplication=appname&poiname=" +
          //   locationName +
          //   "&lat=" +
          //   lat +
          //   "&lon=" +
          //   lon +
          //   "&dev=0";
          url = `amapuri://route/plan/?sourceApplication=amap&sid=&slat=36.1234&slon=116.9087&sname=当前位置&did=&dlat=${lat}&dlon=${lng}&dname=目的地&dev=0&t=0`;
        }
        // 腾讯
        else if (id == 3) {
        }

        window.location.href = url;
      }
      this.show = false;
    },
    // 选择地图
    handleMap() {
      let lat = Number(this.particularsData.latitude);
      let lon = Number(this.particularsData.longitude);
      let locationName = this.particularsData.address;
      this.$wx.miniProgram.navigateTo({
        url: `/pages_saas/navition/index?lat=${lat}&lon=${lon}&locationName=${locationName}`,
        success: (res) => {
          this.handlePageHide()
        },
      });
      // this.jump(item.id);
    },
    // 类型切换
    tabTypePoi(item) {
      if (this.polylineLayer) {
        this.polylineLayer.setMap(null);
        this.polylineLayer = "";
      }
      if (this.audioData) {
        this.audioType = false;
        this.audioData.pause();
      }
      this.removeMarder();
      this.dotType = false;
      this.selectMarkerId = "";
      if (item.id == "") {
        this.poiParams.typeId = "";
        this.geoIndexAll();
        return;
      }
      this.poiParams.typeId = item.id;
      this.geoIndexAll();
    },
    // 路径规划
    getWalking() {
      //  区域边界 如果后台配置区域则生效
      let that = this;
      if (that.tenantData.regionList.length !== 0) {
        let data = that.tMap.getCenter();
        let path = [];
        for (let i = 0; i < that.tenantData.regionList.length; i++) {
          path.push(
            new that.TXMap.LatLng(
              Number(that.tenantData.regionList[i].latitude),
              Number(that.tenantData.regionList[i].longitude)
            )
          );
        }
        let type = that.TXMap.geometry.isPointInPolygon(
          new that.TXMap.LatLng(
            Number(that.locationData.latitude),
            Number(that.locationData.longitude)
          ),
          path
        );
        // console.log(type);
        if (!type) {
          that.popupType = true;
          let time = setTimeout(() => {
            that.popupType = false;
            clearTimeout(time);
          }, 2000);
        } else {
          that.newDrivingsearch();
        }
      }
    },
    // 导航
    newDrivingsearch() {
      let that = this;
      this.drivingsearch(
        Number(this.locationData.longitude),
        Number(this.locationData.latitude),
        Number(this.particularsData.longitude),
        Number(this.particularsData.latitude)
      ).then((res) => {
        // this.polylineHide = true
        // this.polylinePath = res
        // this.$refs.map.$$getInstance().setFitView()
        // this.zoom = 13
        // console.log(res);
        that.polylineLayer = new that.TXMap.MultiPolyline({
          id: "polyline-layer", //图层唯一标识
          map: that.tMap, //绘制到目标地图
          //折线样式定义
          styles: {
            style_blue: new that.TXMap.PolylineStyle({
              color: "#ff6d00", //线填充色
              width: 6, //折线宽度
              borderWidth: 2, //边线宽度
              borderColor: "#FFF", //边线颜色
              lineCap: "butt", //线端头方式
            }),
          },
          //折线数据定义
          geometries: [
            {
              //第1条线
              id: "pl_1", //折线唯一标识，删除时使用
              styleId: "style_blue", //绑定样式名
              paths: res,
            },
          ],
        });
      });
    },
    // 获取路线
    drivingsearch(originLng, originLat, terminusLng, terminusLat) {
      return new Promise((resolved, rejected) => {
        let that = this;
        AMap.plugin(["AMap.Walking"], function () {
          let driving = new AMap.Walking({
            map: that.$refs.map,
            // panel: "panel",
          });
          let lonLatitu = [];
          driving.search(
            new AMap.LngLat(originLng, originLat),
            new AMap.LngLat(terminusLng, terminusLat),
            function (status, result) {
              // // console.log(status, result);
              for (var k = 0; k < result.routes[0].steps.length; k++) {
                for (
                  var h = 0;
                  h < result.routes[0].steps[k].path.length;
                  h++
                ) {
                  lonLatitu.push(
                    new that.TXMap.LatLng(
                      result.routes[0].steps[k].path[h].lat,
                      result.routes[0].steps[k].path[h].lng
                    )
                  );
                }
              }

              if (status == "complete") {
                // // console.log('绘制驾车路线完成');
                resolved(lonLatitu);
              } else {
                // console.log("获取驾车数据失败：", result);
              }
            }
          );
        });
      });
    },
    // 金刚区
    getKongAll() {
      let data = {
        status: 0,
        // sysTenantId: "10687",
        sysTenantId: window.localStorage.getItem("sysTenantId"),
      };
      getKongAll(data).then((res) => {
        // console.log(res);
        for (let i = 0; i < res.data.length; i++) {
          res.data[
            i
          ].icon = `${process.env.VUE_APP_BASE_API}/admin/file/image/${res.data[i].icon}`;
        }
        this.moduleTreeList = res.data;
      });
    },
    // 获取活动数据
    // 类型全部
    getPoiTypeAll() {
      let data = {
        parentId: 1,
        sysTenantId: window.localStorage.getItem("sysTenantId"),
        status: 0,
      };
      getPoiTypeAll(data).then((res) => {
        // console.log(res, "getPoiTypeAll==============>>");
        for (let i = 0; i < res.data.length; i++) {
          res.data[
            i
          ].icon = `${process.env.VUE_APP_BASE_API}/admin/file/image/${res.data[i].icon}`;
        }

        // res.data = res.data.concat(res.data)
        // if (res.data.length > 6) {
        // res.data.push({
        //     id : '',
        //     name : '全部',
        //     icon : 'https://www.dreamdeck.cn:10443/app/icons/southtwoloop/saas_markerall.png'
        // })
        // }

        this.poiTypeList = res.data;

        // if (res.data.length) {
        //   this.poiParams.typeId = res.data[0].id;
        //   this.geoIndexAll();
        // } else {
        //   this.geoIndexAll();
        // }
        this.geoIndexAll();
        // chunkArr(res.data, 6)
      });
    },
    // 根据id查询组织
    async getByTenantId() {
      let data = {
        sysTenantId: window.localStorage.getItem("sysTenantId"),
      };
      let that = this;
      await getByTenantId(data).then(async (res) => {
        if (res.data) {
          this.themList.forEach((item) => {
            if (item.value == res.data.theme) {
              this.themId = item.id;
            }
          });
          this.themId = res.data.theme;
          // 先写死
          await this.getPreferredList();
          await this.getactivityList();
          if (this.themId == "saas_theme_4") {
            await this.getPreferredList();
            await this.getactivityList();
          }
          // console.log(this.themId, document, "document=============");
          this.tenantData = res.data;
          this.mapData.latitude = Number(this.tenantData.latitudeCentre);
          this.mapData.longitude = Number(this.tenantData.longitudeCentre);
          document.title = this.tenantData.topName || this.tenantData.name;
          // document.body.style.backgroundColor =this.themId=='saas_theme_2'?'#ab6b26':this.themId=='saas_theme_3'?'rgb(120, 217, 215)':this.themId=='saas_theme_5'?'rgb(136, 233, 212)':this.themId=='saas_theme_8'?'rgb(225, 255, 251)':'#fff'
          //  document.querySelector('head title').backgroundColor=this.themId=='saas_theme_2'?'#ab6b26':'#fff';
          // console.log(
          //   document.querySelector("head title"),
          //   "document.title======>>"
          // );
          // // console.log(this.tenantData, "this.tenantData=========>>");
          // 指定层级
          if (this.tenantData.miniTier) {
            this.mapData.zoom = Number(this.tenantData.miniTier);
          }

          // 切片
          if (this.tenantData.miniStyleMap) {
            if (window.localStorage.getItem("sysTenantId") == "10723" || window.localStorage.getItem("sysTenantId") == "10205") {
            } else {
              this.mapData.miniStyleMap = this.tenantData.miniStyleMap;
            }
          }
        }
        this.getAnnouncementAll();
      });
    },
    // 获取poi
    geoIndexAll() {
      this.removeMarder()
      // // console.log(this.tMap.getZoom(), this.tMap.getBounds());
      let bounds = this.tMap.getBounds();
      this.poiParams.pointLeft = `${bounds["_sw"].lng},${bounds["_sw"].lat}`;
      this.poiParams.pointRight = `${bounds["_ne"].lng},${bounds["_ne"].lat}`;
      this.poiParams.hierarchy = this.tMap.getZoom();
      this.poiParams.sysTenantId = window.localStorage.getItem("sysTenantId");
      geoIndexAll(this.poiParams).then((res) => {
        // console.log(res);
        if (!res.data.point) return;
        for (let i = 0; i < res.data.point.length; i++) {
          res.data.point[i].selectMarkerId = 0;
          if (res.data.point[i].id == this.selectMarkerId) {
            res.data.point[i].selectMarkerId = this.selectMarkerId;
          }
        }
        this.markerList = JSON.parse(JSON.stringify(res.data.point));
        if (localStorage.getItem("sysTenantId") != '11048' && localStorage.getItem("sysTenantId") != '10205') {
          // this.renderMarker();
          this.$emit('poiRender', this.markerList, 'style1')
        } else {
          this.$emit('poiRender', this.markerList, 'style2')
        }
      });
    },
    // 初始化地图
    initMap() {
      let that = this;
      MapLoader(MAP_KEY).then(async (TMap) => {
        await this.getByTenantId();
        // console.log(TMap);
        that.TXMap = TMap;
        //定义地图中心点坐标
        var center = new TMap.LatLng(
          that.mapData.latitude,
          that.mapData.longitude
        );
        //定义map变量，调用 TMap.Map() 构造函数创建地图
        that.tMap = new TMap.Map(document.getElementById("dd_map_container"), {
          center: center, //设置地图中心点坐标
          zoom: that.mapData.zoom, //设置地图缩放级别
          showControl: false,
          mapStyleId: "style1",
          viewMode: "2D",
          minZoom: that.tenantData.miniTierMin
            ? Number(that.tenantData.miniTierMin)
            : 1,
          // maxZoom: that.tenantData.miniTierMax
          //   ? Number(that.tenantData.miniTierMax)
          //   : 20,
        });
        if (window.localStorage.getItem("sysTenantId") == "10723") {
          var imageSW = new TMap.LatLng(39.840607, 116.697056);
          var imageNE = new TMap.LatLng(39.896412, 116.774458);
          var imageLatLngBounds = new that.TXMap.LatLngBounds(imageSW, imageNE);
          var imageGroundLayer = new that.TXMap.ImageGroundLayer({
            bounds: imageLatLngBounds,
            src: "https://www.dreamdeck.cn:10443/app/icons/greenheart/test11.png",
            map: that.tMap,
            // opacity:0.5
          });
        }
        if (window.localStorage.getItem("sysTenantId") == "10209" || window.localStorage.getItem("sysTenantId") == "10229") {
          var imageSW = new TMap.LatLng(39.79933880155271, 115.52077119488282);
          var imageNE = new TMap.LatLng(39.866320720542394, 115.646996);
          var imageLatLngBounds = new that.TXMap.LatLngBounds(imageSW, imageNE);
          var imageGroundLayer = new that.TXMap.ImageGroundLayer({
            bounds: imageLatLngBounds,
            src: "https://www.dreamdeck.cn:10443/app/icons/sass/flowerMoutain.png",
            map: that.tMap,
            // opacity:0.5
          });
        }
        if (window.localStorage.getItem("sysTenantId") == "11048" || window.localStorage.getItem("sysTenantId") == "10205") {
          var imageSW = new TMap.LatLng(39.840607, 116.697056);
          var imageNE = new TMap.LatLng(39.896412, 116.774458);
          var imageLatLngBounds = new TMap.LatLngBounds(imageSW, imageNE);
          new TMap.ImageGroundLayer({
            bounds: imageLatLngBounds,
            src: "https://www.dreamdeck.cn:10443/app/icons/greenheart/test11.png", //拟覆盖图片的URL
            map: this.tMap,
            zIndex: 10,
          });
          that.tMap.setZoom(14);
          that.tMap.setCenter(new TMap.LatLng(39.875116, 116.730861));
        }
        // 地图点击事件
        that.tMap.on("click", (e) => {
          // console.log(e, "!!!!!!!!!");
          if (!that.markerClickType) {
            // 判断是否有选中  是 则清除上一个选中状态
            if (that.selectMarkerId) {
              let markerData = {
                id: that.selectMarkerId,
                styleId: `marker${that.particularsData.id}`,
                position: new this.TXMap.LatLng(
                  Number(that.particularsData.latitude) * 1,
                  Number(that.particularsData.longitude) * 1
                ),
                content: `${that.particularsData.name}`,
              };
              if (!Array.isArray(this.markerLayer)) that.markerLayer.updateGeometries(markerData);
            }
            that.selectMarkerId = "";
            that.dotType = false;
          }

          if (that.polylineLayer) {
            that.polylineLayer.setMap(null);
            that.polylineLayer = "";
          }

          if (that.audioData) {
            that.audioType = false;
            that.audioData.pause();
          }
        });
        // 地图拖拽结束事件
        that.tMap.on("dragend", (e) => {
          // 清除marker
          that.removeMarder()
          // 重新请求
          that.geoIndexAll()
          //  区域边界 如果后台配置区域则生效
          if (that.tenantData.regionList.length) {
            let data = that.tMap.getCenter();
            let path = [];
            for (let i = 0; i < that.tenantData.regionList.length; i++) {
              path.push(
                new that.TXMap.LatLng(
                  Number(that.tenantData.regionList[i].latitude),
                  Number(that.tenantData.regionList[i].longitude)
                )
              );
            }
            let type = that.TXMap.geometry.isPointInPolygon(
              new that.TXMap.LatLng(Number(data.lat), Number(data.lng)),
              path
            );
            if (!type) {
              // that.tMap.setZoomAndCenter(that.zoom, that.center)
              that.tMap.setCenter(
                new that.TXMap.LatLng(
                  Number(that.mapData.latitude),
                  Number(that.mapData.longitude)
                )
              );
              that.tMap.setZoom(that.mapData.zoom);
            }
          }
        });
        // 地图缩放事件
        let type = true;
        that.tMap.on("zoom", that.debounce(500));
        this.initMachineSVGA();
        // 获取poi
        // that.geoIndexAll()

        // 获取类型
        that.getPoiTypeAll();

        // 判断是否 配置个性化图层
        if (that.mapData.miniStyleMap) {
          that.renderLayer();
        }

        // 获取当前位置
        // that.geolocation = new qq.maps.Geolocation('NJRBZ-6MULQ-QXI5Y-2WI2V-WCFWS-RQFK5', 'myapp');
        that.getMyLocation();
        this.$emit("initMap");
      });
    },
    // 移除地图所有marker
    removeMarder() {
      if (this.markerLayer) {
        // this.markerLayer.map(i => {
        //   i.setMap(null);
        // })
        // this.markerLayer = [];
      }
    },
    // 地图缩放 防抖处理
    debounce(delay) {
      let timer = null;
      let that = this;
      return function () {
        if (timer) {
          clearTimeout(timer); //如果方法多次触发，则把上次记录的延迟执行代码清掉，重新开始
        }
        timer = setTimeout(() => {
          that.removeMarder();
          that.geoIndexAll();
        }, delay);
      };
    },
    // 获取当前位置
    getMyLocation() {
      let that = this;
      // this.geolocation.getLocation(this.showPosition, this.errorPosition); //开启定位
      navigator.geolocation.getCurrentPosition(
        that.showPosition,
        that.errorPosition
      );
    },
    // 定位成功
    showPosition(data) {
      let that = this;
      let position = data.coords;
      this.locationData = position;
      // console.log("定位成功", position);
      if (position) {
        // 定位成功侯进行判断   如果用户在区域边界内 则为当前地图中心点  反之进行获取
        if (that.tenantData.regionList.length) {
          let path = [];
          for (let i = 0; i < that.tenantData.regionList.length; i++) {
            path.push(
              new that.TXMap.LatLng(
                Number(that.tenantData.regionList[i].latitude),
                Number(that.tenantData.regionList[i].longitude)
              )
            );
          }
          let type = that.TXMap.geometry.isPointInPolygon(
            new that.TXMap.LatLng(
              Number(position.latitude),
              Number(position.longitude)
            ),
            path
          );
          // console.log(type);
          if (type) {
            // that.tMap.setZoomAndCenter(that.zoom, that.center)
            that.tMap.setCenter(
              new that.TXMap.LatLng(
                Number(position.latitude),
                Number(position.longitude)
              )
            );
            that.tMap.setZoom(that.mapData.zoom);
          }
        }

        // 渲染 动态png
        this.createDomMarker(position.latitude, position.longitude);
      }
    },
    createDomMarker(lat, lng) {
      let that = this;
      if (document.getElementById("grad2")) {
        document.getElementById("grad2").remove();
      }
      // 渲染动态png
      createMarker(that.TXMap, {
        //点标注数据数组WaterRipple
        map: that.tMap,
        position: new that.TXMap.LatLng(lat, lng), // 动态图放置位置
        icon: require("../assets/location.png"), // 路径
      });
    },
    // 定位失败
    errorPosition(e) {
      // console.log("定位失败，再次进行定位", e);
    },
    // 监听位置
    showWatchPosition() {
      // console.log("监听成功");
      this.geolocation.watchPosition(this.showPosition);
    },
    // 停止监听
    showClearWatch() {
      this.geolocation.clearWatch();
    },
    // 渲染 layer
    renderLayer() {
      let that = this;
      // console.log("走这里了");
      new TMap.WMSLayer({
        url: that.tenantData.miniStyleUrl,
        map: that.tMap,
        params: {
          VERSION: "1.3.0",
          LAYERS: that.mapData.miniStyleMap,
          // 'TZGY:lx0828',超出
          FORMAT: "image/png",
        },
      });
    },
    // 跳转商户详情
    getCommercial() {
      // console.log(this.particularsData.tentId);
      if (this.particularsData.type == 4) {
        this.$wx.miniProgram.navigateTo({
          url: `/product_lease/tent/appointment/appointment?id=${this.particularsData.tentId
            }&sysTenantId=${window.localStorage.getItem(
              "sysTenantId"
            )}&channelId=${this.tenantData.channelId}`,
          success: (res) => {
            this.handlePageHide()
          },
        });
        return;
      }
      this.$wx.miniProgram.navigateTo({
        url: `/pages_saas/commercial/commercial?id=${this.particularsData.id
          }&sysTenantId=${window.localStorage.getItem("sysTenantId")}&channelId=${this.tenantData.channelId
          }`,
        success: (res) => {
          this.handlePageHide()
        },
      });
    },
    // 跳转活动详情
    navActivity(item) {
      this.$wx.miniProgram.navigateTo({
        url: `/pages_saas/themeTemplate/flowerTheme/activity/activityDetail?sysTenantId=${window.localStorage.getItem(
          "sysTenantId"
        )}&id=${item.id}`,
        success: (res) => {
          this.handlePageHide()
        },
      });
    },
    // 跳转个人中心
    getOneself() {
      this.$wx.miniProgram.reLaunch({
        url: `/pages_saas/oneself/oneself?preRoute=1&sysTenantId=${window.localStorage.getItem(
          "sysTenantId"
        )}&channelId=${this.tenantData.channelId}`,
        success: (res) => {
          this.handlePageHide()
        },
      });
    },
    // 跳转首页
    getHome() {
      this.$wx.miniProgram.reLaunch({
        url: `/pages_saas/home/<USER>
          "sysTenantId"
        )}&channelId=${this.tenantData.channelId}`,
        success: (res) => {
          this.handlePageHide()
        },
      });
    },
    // 跳转订单列表
    getOrder() {
      this.$wx.miniProgram.reLaunch({
        url: `/pages_saas/order/order?preRoute=1`,
        success: (res) => {
          this.handlePageHide()
        },
      });
    },
    // 跳转门票列表
    getTicket(item) {
      // console.log(item, "###############");
      if (item.formContentType == 1) {
        this.$wx.miniProgram.navigateTo({
          // url: `/pages_saas/commercial/commercial?sysTenantId=${window.localStorage.getItem(
          //   "sysTenantId"
          // )}&id=${item.id}`
          url: `/pages_saas/commercial/commercial?id=${item.labels[0]
            }&sysTenantId=${window.localStorage.getItem("sysTenantId")}`,
          success: (res) => {
            this.handlePageHide()
          },
        });
        return;
      }

      if (item.formContentType == 2) {
        this.$wx.miniProgram.navigateTo({
          url: `/pages_banner/particulars/particulars?forwardUrl=${item.url}`,
        });
        return;
      }
      if (item.formContentType == 3) {
        this.$wx.miniProgram.navigateTo({
          url: `${item.url}`,
          success: (res) => {
            this.handlePageHide()
          },
        });
        return;
      }
      // item.formContentType == 4自定义提示语
      if (item.formContentType == 4) {
        // console.log(item.url, "item.url");
        this.messageNoctice = item.url;
        this.messageNocticeShow = true;
        setTimeout(() => {
          this.messageNocticeShow = false;
        }, 3000);
        // this.$wx.showModal({
        // 		icon: 'none',
        // 		title: item.url,
        // 		duration: 3000,
        // 		mask: true

        // 	})
        // this.$message({
        //   message: item.url,
        //   duration: 2000,
        //   offset: 150,
        // });
        return;
      }
      // item.formContentType == 5门店详情
      if (item.formContentType == 5) {
        this.$wx.miniProgram.navigateTo({
          // url: `/pages_saas/commercial/commercial?sysTenantId=${window.localStorage.getItem(
          //   "sysTenantId"
          // )}&id=${item.id}`
          url: `/pages_saas/commercial/commercial?id=${item.labels[0]
            }&sysTenantId=${window.localStorage.getItem("sysTenantId")}`,
        });
        return;
      }
      // 内容
      if (item.formContentType == 6) {
        this.$wx.miniProgram.navigateTo({
          url: `/pages_kong/content/content?id=${item.id}`,
          success: (res) => {
            this.handlePageHide()
          },
        });
        return;
      }
      this.$wx.miniProgram.navigateTo({
        url: `/pages_saas/shop/shop?sysTenantId=${window.localStorage.getItem(
          "sysTenantId"
        )}&formType=${item.formType}&title=${item.name}&kongId=${item.id
          }&longitue=${this.locationData.longitude}&latitude=${this.locationData.latitude
          }&channelId=${this.tenantData.channelId}`,
        success: (res) => {
          this.handlePageHide()
        },
      });
    },
    // 点击预定
    reserve() {
      this.$wx.miniProgram.navigateTo({
        url: `/pages_ticket/appointment/appointment`,
        success: (res) => {
          this.handlePageHide()
        },
      });
    },
    // 拨打电话
    callPhone(phoneNumber) {
      // console.log(phoneNumber);
      window.location.href = "tel://" + phoneNumber;
    },
    // 点击简介
    getSearch() {
      this.$wx.miniProgram.navigateTo({
        url: `/pages_saas/search/search?sysTenantId=${window.localStorage.getItem(
          "sysTenantId"
        )}&channelId=${this.tenantData.channelId}`,
        success: (res) => {
          this.handlePageHide()
        },
      });
    },
    // dom 拖拽
    // 拖拽开始
    touchstart(e) {
      if (this.themId != "saas_theme_4") {
        return;
      }
      // console.log(e, "拖拽开始");
      // console.log(e.changedTouches[0].clientY, '拖拽开始');
      let height = document.body.clientHeight;
      this.touchTypeY = (100 / height) * e.changedTouches[0].clientY;
      this.touchY =
        (100 / height) * e.changedTouches[0].clientY - this.contentTop;

      let dom = document.querySelector(".activity_outer");
      dom.style.overflow = "hidden";
      // this.$refs.mapContent.style.overflow = 'hidden';
      // this.contentTop = e.changedTouches[0].clientY - this.touchY
    },
    // 拖拽中
    touchmove(e) {
      if (this.themId != "saas_theme_4") {
        return;
      }
      let height = document.body.clientHeight;
      if (!this.slideType) {
        if ((100 / height) * e.changedTouches[0].clientY > this.touchTypeY) {
          if (
            (100 / height) * e.changedTouches[0].clientY - this.touchTypeY >
            0
          ) {
            this.slideType = true;
            this.contentTop =
              (100 / height) * e.changedTouches[0].clientY - this.touchY;
          }
        }
        return;
      }

      // console.log(height / e.changedTouches[0].clientY, height / e.changedTouches[0].clientY / 100, this.touchY);
      this.contentTop =
        (100 / height) * e.changedTouches[0].clientY - this.touchY;
      // 不能过顶
      if (this.contentTop < 0) {
        this.contentTop = 0;
      }
    },
    // 拖拽结束
    touchend(e) {
      if (this.themId != "saas_theme_4") {
        return;
      }
      let dom = document.querySelector(".activity_outer");
      dom.style.overflow = "scroll";
      if (!this.slideType) {
        return;
      }
      if (
        e.changedTouches[0].clientY < 1 ||
        (e.changedTouches[0].clientY < 500 && e.changedTouches[0].clientY > 10)
      ) {
        // console.log("顶部");
        // this.$refs.mapContent.style.overflow = 'auto';
        this.contentTop = 2;
        this.slideType = false;
        this.$refs.gather.style.overflow = "auto";
      } else {
        // console.log("底部");
        // this.$refs.mapContent.style.overflow = 'hidden';
        this.contentTop = this.offsetHeightVh;
        this.slideType = true;
      }

      this.$refs.mapContent.style.transition = "all 0.5s";

      setTimeout(() => {
        this.$refs.mapContent.style.transition = "";
      }, 500);
      // if (e.changedTouches[0].clientY > 200 && e.changedTouches[0].clientY < 741) {
      //     // console.log(e, '拖拽结束');
      //     this.contentTop = e.changedTouches[0].clientY - this.touchY
      // }
    },
    // 滚动事件
    ticketScroll(e) {
      // console.log(e, "ticketScroll");
    },
    // 添加事件
    addEvent() {
      this.$refs.gather.style.overflow = "hidden";
      this.$refs.mapContent.addEventListener("touchstart", this.touchstart);
      this.$refs.mapContent.addEventListener("touchmove", this.touchmove);
      this.$refs.mapContent.addEventListener("touchend", this.touchend);
    },
    // 移除事件
    removeEvent() {
      this.$refs.mapContent.removeEventListener("touchstart", this.touchstart);
      this.$refs.mapContent.removeEventListener("touchmove", this.touchmove);
      this.$refs.mapContent.removeEventListener("touchend", this.touchend);
    },
    tabTouchstart(e) { },
    tabTouchmove(e) { },
    tabTouchend(e) { },
  },
};
</script>
<style lang="scss" src="../static/map.scss"></style>

<style scoped lang="scss">
.dd_map {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

#dd_map_container {
  width: 100%;
  height: 100%;
}

.dd_map_container {
  width: 100%;
  height: 100vh;
  /* position: fixed;
        top: 0;
        left: 0;
        z-index: -1; */
}

.dd_map_content_flower::after {
  display: block;
  content: "";
  width: 24px;
  height: 6px;
  position: absolute;
  left: 50%;
  top: 6px;
  margin: 0 0 0 -12px;
  background: url("https://www.dreamdeck.cn:10443/app/icons/southtwoloop/saas_arrows.png") no-repeat 100%/100%;
}

/* .el-vue-amap-container{
        width: 100%;
        height: 100%;
        z-index: -1;
    } */
.catory_outer {
  width: 100%;
  height: 56px;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 10px 0;
  box-sizing: border-box;
  z-index: 9999;
  overflow-y: auto;
  overflow-y: hidden;

  .dd_map_tab_type_flex_list {
    padding: 0;
  }
}

.dd_map_tab_type {
  width: 100%;
  height: 56px;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 10px 0;
  box-sizing: border-box;
  z-index: 9999;
}

.dd_map_tab_type_back {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background: #fff;
  /* background: linear-gradient(180deg, #4fca7c 0%, rgba(117, 219, 173, 0.8) 99%)*/
  /* border-bottom: 1px solid #fff; */
}

.dd_map_tab_type_back>div {
  width: 100%;
  height: 1px;
  position: absolute;
  left: 0;
  bottom: -1px;
  background: linear-gradient(270deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 1) 48%,
      rgba(255, 255, 255, 0) 100%);
}

.dd_map_tab_type_absolute {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 999;
  padding: 0 20px;
  box-sizing: border-box;
}

.dd_map_tab_type_absolute_doot {
  width: 60px;
  position: absolute;
  bottom: 4px;
  left: 50%;
  margin: 0 0 0 -30px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #2020205f;
  border-radius: 30px;
}

.dd_map_tab_type_absolute_doot div {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: #ff6d00;
  margin: 0 3px;
  transform: all 1s;
}

.dd_map_tab_type_absolute_doot .dd_map_tab_type_absolute_doot_select {
  /* width: 15px; */
  background: #ffffff;
  border: 0.5px solid rgba(255, 109, 0, 0.6);
  box-sizing: border-box;
  border-radius: 30px;
  transform: all 1s;
}

.dd_map_tab_type_flex {
  display: flex;
  align-items: center;
  /* justify-content: space-between; */
  flex: 1;
  overflow: auto;
  height: 100%;
}

/* .dd_map_tab_type_flex::-webkit-scrollbar{
        display: none;
    } */
.dd_map_tab_type_all {
  opacity: 1;
}

.dd_map_tab_type_flex_list {
  margin: 0 15px;
  height: 100%;
  padding: 4px 0 0 0;
  box-sizing: border-box;
}

.dd_map_tab_type_flex>.dd_map_tab_type_flex_list:nth-of-type(1) {
  margin-left: 0;
}

.dd_map_tab_type_absolute>.dd_map_tab_type_flex_list {
  margin-right: 0;
}

.dd_map_tab_type_flex_list_opacity {
  opacity: 1 !important;
  position: relative;
}

.dd_map_tab_type_flex_list_opacity_dyh {
  font-family: "SourceHanSansCN-Regular" !important;
  opacity: 1 !important;
  position: relative;
}

.dd_map_tab_type_flex_list_opacity_lt,
.dd_map_tab_type_flex_list_opacity_lx {
  opacity: 1 !important;
  position: relative;
}

.dd_map_tab_type_flex_list_opacity::after {
  width: 100%;
  height: 3px;
  background: #fff;
  border-radius: 60px;
  display: block;
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
}

.dd_map_tab_type_flex_list_opacity_dyh::after {
  width: 100%;
  height: 3px;
  background: #a96640;
  border-radius: 60px;
  display: block;
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
}

.dd_map_tab_type_flex_list_opacity_lt::after {
  width: 100%;
  height: 3px;
  background: #12c5b6;
  border-radius: 60px;
  display: block;
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
}

.dd_map_tab_type_flex_list_opacity p {
  /* color: #00B150 !important; */
  /* text-shadow: #00B04F 1px 0px 0px; */
  font-weight: bold;
  color: #fff !important;
  /* color: #a96640 !important; */
}

.dd_map_tab_type_flex_list_opacity_dyh p {
  font-weight: bold;
  color: #a96640 !important;
  font-family: "SourceHanSansCN-Regular";
}

.dd_map_tab_type_flex_list_opacity_lt p {
  font-weight: bold;
  color: #12c5b6 !important;
  font-family: "NotoSansCJK-Regular" !important;
}

/* .dd_map_tab_type_flex_list_opacity .dd_map_tab_type_flex_list_img{
        position: relative;
        border: 2px solid #fff;
        border-radius: 8px;
        box-sizing: border-box;
    } */

/* .dd_map_tab_type_flex_list_opacity .dd_map_tab_type_flex_list_img::after{
        width: 28px;
        height: 28px;
        border: 2px solid #00C465;
        border-radius: 8px;
        display: block;
        content: '';
        position: absolute;
        left: -2px;
        top: -4px;
    } */
.dd_map_tab_type_flex_list_img {
  width: 30px;
  height: 30px;
  display: block;
  margin: auto;
  box-sizing: border-box;
  padding: 2px;
}

.dd_map_tab_type_flex_list_dyh_p {
  font-family: "SourceHanSansCN-Regular";
}

.dd_map_tab_type_flex_list img,
.dd_map_tab_type_all img {
  width: 100%;
  height: 100%;
}

.dd_map_tab_type_flex_list p,
.dd_map_tab_type_all p {
  color: #333333;
  text-align: center;
  font-size: 12px;
  margin: 2px 0 0 0;
  width: 100%;
  white-space: nowrap;
  white-space: nowrap;
  max-width: 70px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dd_map_search {
  width: calc(100% - 18px);
  position: fixed;
  top: 10px;
  left: 9px;
  border: 1px solid #ffffff;
  border-radius: 460px;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 12px;
  box-sizing: border-box;
}

.dd_map_search_flex {
  flex: 1;
  display: flex;
  align-items: center;
}

.dd_map_search_flex img {
  width: 25px;
  height: 25px;
  filter: brightness(0.8);
}

.dd_map_search_flex input {
  flex: 1;
  display: block;
  height: 25px;
  margin: 0 0 0 7px;
  border: none !important;
  background: none !important;
  outline: none;
  color: rgba(55, 203, 123, 0.5);
}

.dd_map_search_flex input::placeholder {
  color: rgba(55, 203, 123, 0.5);
}

.dd_map_search p {
  font-size: 14px;
  color: #37cb7b;
  margin: 0 0 0 10px;
}

.dd_map_content_dyh {
  width: 100%;
  height: 100vh;
  position: absolute;
  left: 0;
  top: 80px;
  /* background:#FFFFFF; */
  background-image: url("https://www.dreamdeck.cn:10443/app/icons/sass/map/dyh/dyhbgc.png") !important;
  border-radius: 20px;
  background-repeat: no-repeat !important;
  background-size: contain !important;
  z-index: 999999;
  /* border: 1px solid #fff; */
  /* box-sizing: border-box; */
  font-family: "SourceHanSansCN-Regular";
}

.dd_map_content_lt {
  width: 100%;
  height: 100vh;
  position: absolute;
  left: 0;
  top: 80px;
  /* background:#FFFFFF; */
  background-image: url("https://www.dreamdeck.cn:10443/app/icons/sass/ltbgc.png") !important;
  border-radius: 20px;
  background-repeat: no-repeat !important;
  background-size: contain !important;
  z-index: 999999;
  /* border: 1px solid #fff; */
  /* box-sizing: border-box; */
  /* font-family: "SourceHanSansCN-Regular"; */
}

.dd_map_content_lx {
  width: 100%;
  height: 100vh;
  position: absolute;
  left: 0;
  top: 80px;
  /* background:#FFFFFF; */
  background-image: url("https://www.dreamdeck.cn:10443/app/icons/sass/lxbgc.png") !important;
  border-radius: 20px;
  background-repeat: no-repeat !important;
  background-size: contain !important;
  z-index: 999999;
  /* border: 1px solid #fff; */
  /* box-sizing: border-box; */
  /* font-family: "SourceHanSansCN-Regular"; */
}

.dd_map_content {
  width: 100%;
  height: 100vh;
  position: absolute;
  left: 0;
  /* top: 80px; */
  background: #ffffff;
  border-radius: 20px;
  z-index: 999999;
  border: 1px solid #fff;
  box-sizing: border-box;
}

.dd_map_content_type {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 10px 0 10px;
  box-sizing: border-box;
  margin: 0 0 20px 0;
}

.dd_map_content_type_auto {
  display: flex;
  align-items: center;
  padding: 16px 10px 0 10px;
  box-sizing: border-box;
  margin: 0 0 20px 0;
  white-space: nowrap;
  overflow: auto;
}

.dd_map_content_type_list {
  width: 20%;
  margin: 4px 0 0 0;
}

.dd_map_content_type_auto .dd_map_content_type_list {
  margin-right: 40px !important;
}

.dd_map_content_type_auto .dd_map_content_type_list:nth-last-of-type(1) {
  margin-right: 0 !important;
}

.dd_map_content_type_auto::-webkit-scrollbar {
  display: none;
}

.dd_map_content_type_list>img {
  width: 52px;
  height: 52px;
  display: block;
  margin: auto;
}

.dd_map_content_type_list>p {
  font-size: 13px;
  /* color: #ab6b26; */
  text-align: center;
  margin: 3px 0 0 0;
}

.dd_map_content_type_list_dyh>p {
  font-size: 13px;
  color: #ab6b26 !important;
  text-align: center;
  margin: 3px 0 0 0;
}

.dd_map_content_gather {
  /* min-height: 100vh; */
  height: 76vh;
  padding: 13px 10px;
  box-sizing: border-box;
  /* background: linear-gradient(178deg, #D4FCFC 1%, #F7F9F9 45%, #F7F9F9 98%); */
  border-radius: 20px 20px 0 0;
  /* border-image: linear-gradient(180deg, #FFFFFF 0%, rgba(255,255,255,0.00) 100%) ; */
}

.dd_map_content_gather_flex {
  display: flex;
  align-items: center;
}

.dd_map_content_gather_flex p {
  color: #666666;
  font-size: 16px;
  margin: 0 20px 0 0;
}

.dd_map_content_gather_flex p:nth-last-of-type(1) {
  margin: 0;
}

.dd_map_content_gather_flex_pitch {
  font-size: 18px !important;
  color: #333333 !important;
  position: relative;
  z-index: 99;
}

/* .dd_map_content_gather_flex_pitch::after{
        display: block;
        content: '';
        width: 100%;
        height: 7px;
        background: #37CB7B;
        border-radius: 4px;
        position: absolute;
        left: 0;
        bottom: -2px;
        z-index: -1;
    } */
.dd_map_content_gather_ticket {
  /* height: 20vh;
        overflow: hidden; */
  max-height: 400px;
  overflow: auto;
  padding: 0 0 62px 0;
  box-sizing: border-box;
}

.dd_map_content_gather_ticket_list {
  width: 100%;
  border-radius: 10px;
  box-shadow: 0px 8px 20px 0px rgba(0, 0, 0, 0.04);
  border: 1px solid #ffffff;
  background: rgba(255, 255, 255, 0.7);
  padding: 15px 10px;
  box-sizing: border-box;
  display: flex;
  margin: 10px 0 0 0;
}

.dd_map_content_gather_ticket_list>img {
  width: 70px;
  height: 70px;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  margin: 0 10px 0 0;
}

.dd_map_content_gather_ticket_list_center_title {
  display: flex;
  align-items: center;
}

.dd_map_content_gather_ticket_list_center_title>p:nth-of-type(1) {
  color: #3d3d3d;
  font-size: 14px;
  font-weight: 500;
}

.dd_map_content_gather_ticket_list_center_title>p:nth-of-type(2) {
  color: #3d3d3d;
  font-size: 14px;
}

.dd_map_content_gather_ticket_list_center_label {
  display: flex;
  align-items: center;
}

.dd_map_content_gather_ticket_list_center_label p {
  font-size: 12px;
  color: #999999;
  border-radius: 2px;
  border: 0.2px solid #999999;
  padding: 2px 6px;
  box-sizing: border-box;
}

.dd_map_content_gather_ticket_list_center_money {
  display: flex;
  align-items: center;
  margin: 10px 0 0 0;
}

.dd_map_content_gather_ticket_list_center_money span {
  font-size: 16px;
  color: #fc3e5a;
}

.dd_map_content_gather_ticket_list_center_money span:nth-of-type(1) {
  font-size: 12px;
}

.dd_map_content_gather_ticket_list_button {
  flex: 1;
  display: flex;
  justify-content: right;
  align-items: flex-end;
}

.dd_map_content_gather_ticket_list_button p {
  display: inline-block;
  width: 67px;
  height: 32px;
  border-radius: 4px;
  font-size: 16px;
  line-height: 32px;
  text-align: center;
  background: #ff7d20;
  color: #fff;
}

.dd_map_floor {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 60px;
  z-index: 9999999;
  padding: 10px 25px 10px 25px;
  box-sizing: border-box;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.12);
}

.dd_map_floor_dyh {
  font-family: "SourceHanSansCN-Regular" !important;
}

.dd_map_floor img {
  width: 20px;
  height: 20px;
  display: block;
  margin: auto;
}

.dd_map_floor_select_dyh {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  text-align: center;
  /*  background: #ffeed9; */
  /*  filter: blur(0px); */
}

.dd_map_floor_select {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  text-align: center;
  /* background: #d9f4ff; */
  /* filter: blur(0px); */
}

.dd_map_floor_select img {
  width: 24px !important;
  height: 24px !important;
  display: block;
  margin: auto;
  margin-top: 4px;
}

.dd_map_floor_select p {
  color: #666666 !important;
}

.dd_map_floor p {
  margin: 2px 0 0 0;
  font-size: 12px;
  color: #333333;
}

.dd_map_floor>div:nth-of-type(2) p,
.dd_map_floor>div:nth-of-type(3) p {
  font-size: 12px;
  /* color: #666; */
}

.dd_map_right_position {
  position: fixed;
  right: 20px;
  top: 46vh;
  z-index: 9999;
  opacity: 1;
  transition: all 0.5s;
  min-height: 100px;
}

.dd_map_right_position_all {
  opacity: 0;
}

.dd_map_wire {
  background: #fff;
  border-radius: 12px;
  width: 40px;
  height: 40px;
  box-shadow: 0px 8px 20px 0px rgba(0, 0, 0, 0.3);
  box-sizing: border-box;
  /* position: fixed;
        left: 10px;
        bottom: 160px; */
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: center;
  margin: 0 0 16px 0;
}

.dd_map_wire>img {
  width: 24px;
  height: 24px;
  display: block;
  margin: auto;
  filter: brightness(0.3);
}

.dd_map_wire>p {
  color: #3d3d3d;
  font-size: 12px;
  text-align: center;
  margin: auto;
}

/* .dd_map_scanCode{
        left: auto !important;
        right: 10px !important;
        bottom: 352px !important;
    }


    .dd_map_synopsis{
        left: auto !important;
        right: 10px !important;
        bottom: 412px !important;
    }

    .dd_map_synopsis img{
        width: 20px;
        height: 20px;
        filter: brightness(0.8);
    }

    .dd_map_location{
        left: auto !important;
        right: 10px !important;
        bottom: 292px !important;
    } */

/* poi 详情 */
.dd_map_particulars {
  position: fixed;
  left: 2%;
  bottom: 72px;
  width: 96%;
  /* overflow-y: auto; */
  background: #fff;

  box-sizing: border-box;
  border-radius: 10px;
  z-index: 99999;
  padding: 20px 20px 0 20px !important;
  // height: 175px !important;
}

.dd_map_particulars_facility {
  text-align: center;
  padding: 10px 5px;
  box-sizing: border-box;
}

.dd_map_particulars_facility p {
  padding: 10px 0;
  box-sizing: border-box;
  font-size: 12px;
}

.dd_map_particulars_facility p:nth-of-type(2) {
  border-top: 1px solid #03bfc5;
}

.dd_map_particulars_flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 17px;
  box-sizing: border-box;
  border-top: 1px solid #d8d8d8;
}

.dd_map_particulars_flex>div {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.dd_map_particulars_flex>div img {
  width: 18px;
  height: 18px;
}

.dd_map_particulars_flex>div p {
  font-size: 14px;
  color: #021408;
  margin: 0 0 0 5px;
}

/* .dd_map_particulars_height{
        height: 120px !important;
    } */
.dd_map_particulars_poi {
  display: flex;
  /* align-items: center; */
}

.dd_map_particulars_poi>img {
  width: 70px;
  height: 70px;
  border-radius: 14px;
}

.dd_map_particulars_top {
  display: flex;
  align-items: center;
  // padding: 20px 20px 0 20px;
  padding: 0 !important;
  box-sizing: border-box;
}

.dd_map_particulars_top_one {
  flex: 1;
}

.dd_map_particulars_audio {
  margin: 0 0 0 14px;
}

.dd_map_particulars_audio img {
  width: 40px;
  height: 40px;
}

.dd_map_particulars_audio p {
  font-size: 12px;
  text-align: center;
  color: #999999;
}

.dd_map_particulars_address {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.dd_map_particulars_address img {
  width: 11px;
  height: 15px;
}

.dd_map_particulars_address p {
  font-size: 12px;
  color: #999;
  margin: 0 0 0 7px;
}

.dd_map_particulars_poi_text {
  flex: 1;
  margin: 0 0 0 10px;
}

.dd_map_particulars_poi_text>p:nth-of-type(1) {
  font-size: 14px;
  color: #1a1a1a;
}

.dd_map_particulars_poi_text>p:nth-of-type(2) {
  font-size: 12px;
  line-height: 20px;
  color: #999999;
  margin: 8px 0 0 0;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.dd_map_particulars_poi_button {
  height: 40px;
  font-size: 14px;
  color: #fff;
  padding: 10px 20px;
  box-sizing: border-box;
  background: #679f6a;
  border-radius: 14px;
}

.dd_map_particulars_introduce {
  margin: 16px 0 0 0;
  font-size: 10px;
  line-height: 18px;
  color: #313131;
}

.dd_map_content_gather_tent_list {
  width: 100%;
  height: 180px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  margin: 20px 0;
}

.dd_map_content_gather_tent_list>img {
  width: 100%;
  height: 100%;
}

.dd_map_content_gather_tent_list>div {
  width: 100%;
  height: 52px;
  background: rgba(0, 0, 0, 0.4);
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 9;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-sizing: border-box;
}

.dd_map_content_gather_tent_list>div>div>p {
  color: #fff;
}

.dd_map_content_gather_tent_list>div>div>p:nth-of-type(1) {
  font-size: 16px;
}

.dd_map_content_gather_tent_list>div>div>p:nth-of-type(2) {
  font-size: 10px;
}

.dd_map_content_gather_tent_list>div>p {
  font-size: 12px;
  color: #fff;
}

.dd_map_content_gather_tent_list>div>p>span {
  font-size: 16px;
}

/* 大运河样式处理 */
.dd_map_tab_type_dyh .dd_map_tab_type_back {
  /* background: linear-gradient(180deg, #d9a059 0%, #edc4a3 100%) !important; */
  background: linear-gradient(180deg, #fdecc9 0%, #eacf8d 100%);
}

.dd_map_tab_type_dyh .dd_map_tab_type_flex_list_opacity p {
  text-shadow: #ad7e2e 1px 0px 0px !important;
}

/* 龙潭中湖 */
.dd_map_tab_type_dragon .dd_map_tab_type_back {
  /* background: linear-gradient(180deg, #74d5d9 0%, #4cc7cb 99%) !important; */
  background: #fdfdfd;
}

.dd_map_popup {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99999999;
  width: 100%;
  height: 100%;
  /* background: red; */
  display: flex;
  align-items: center;
  justify-content: center;
}

.dd_map_popup>div {
  width: 40%;
  padding: 20px 0;
  box-sizing: border-box;
  text-align: center;
  font-size: 16px;
  color: #fff;
  background: #000000b4;
  border-radius: 10px;
}

.dd_map_tab_type_notice {
  position: fixed;
  top: 70px;
  left: 50%;
  transform: translateX(-50%);
  padding: 0 10px;
  width: 90%;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: auto;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.7);
  border: 2px solid #ffffff;
  backdrop-filter: blur(30px);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.06);
  color: #4a4a4a;
  font-size: 0.75rem;
  z-index: 9999;
}

.dd_map_tab_type_notice>div:nth-of-type(1) {
  display: flex;
  align-items: center;
  width: 95%;
}

.el-carousel {
  height: 25px;
  width: 90%;
  overflow: hidden;
}

.el-carousel__item div {
  color: #475669;
  font-size: 10px;
  opacity: 0.75;
  line-height: 25px;
  margin: 0;
}

.el-carousel__item:nth-child(2n) {
  // background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
  // background-color: #d3dce6;
}

.dd_map_tab_type_notice_scroll {
  width: 100%;
  // overflow: hidden;
  // white-space: nowrap;
  // text-overflow: ellipsis;
  /* width: 55%; */

  /* animation-iteration-count: 1; */
}

.dd_map_tab_type_notice_scroll>div {
  //-webkit-animation: 10s scroll linear infinite normal;
  //animation: 10s scroll linear infinite normal;
}

@keyframes scroll {
  0% {
    -webkit-transform: translateX(90%);
  }

  100% {
    -webkit-transform: translateX(-120%);
  }
}

.dd_map_tab_type_notice>div:nth-of-type(1)>img {
  width: 18px;
  height: 17px;
  margin-right: 6px;
}

.dd_map_tab_type_notice_close {
  width: 11px !important;
  height: 10px !important;
}

.messageNoctice {
  position: fixed;
  /* height: 50px; */
  width: 145px;
  padding: 10px;
  display: flex;
  align-items: center;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.4);

  border-radius: 10px;
  color: #fff;
  justify-content: center;
  text-align: center;
  z-index: 99;
  /* margin: 0 auto; */
  /* line-height: 40px; */
}

.dd_map_tabbar {
  position: absolute;
  z-index: 9999;
}

.month {
  position: absolute;
  top: 70px;
  z-index: 9999;
  /* width: 100%; */
  display: flex;
  overflow: auto;
  width: 400px;
}

.month_item {
  height: 32px;
  display: inline-block;
  text-align: center;
  line-height: 32px;
  background-position: 0 0;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 0 15px;
  min-width: 40px;
  font-size: 12px;
}

.catory_outer {
  width: 100%;
}

.activity_outer {
  display: flex;
  // align-items: center;
  justify-content: space-between;
  padding: 16px 10px 0 10px;
  box-sizing: border-box;
  margin: 0 0 20px 0;
  width: 100%;
  box-sizing: border-box;
  flex-wrap: wrap;
  // height: calc(100% - 70px);
  overflow: scroll;
}

.activity_item {
  width: calc(50% - 16px);
  padding: 10px 5px;
  font-size: 12px;
  color: #767676;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0px 0px 9.86px 0px rgba(0, 0, 0, 0);

  .dd-drawer-content-item-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 9px;
  }
}

.activity_item img {
  border-radius: 10px 10px 0px 0px;
  overflow: hidden;
}

.activity_item .dd-drawer-content-item-title {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin: 3px 0;
}

.tag-list {
  display: flex;

  div {
    // line-height: 10rpx;
    color: rgb(255, 149, 149);
    background: rgb(255, 238, 238);
    margin-right: 5px;
    padding: 0rpx 3px;
    border-radius: 5px;
    height: 14px;
    font-weight: 80 !important;
    // line-height: 1;
  }
}

.zan {
  display: flex;
  align-items: center;
  margin-right: 5px;
  font-size: 10px;
  height: 10px;
  vertical-align: middle !important;

  div {
    font-size: 10px;
    line-height: 10px;

    vertical-align: middle !important;
  }

  img {
    vertical-align: middle !important;
  }

  .zan-icon {
    margin-right: 10px;
    vertical-align: middle !important;
  }
}

.catory_item {
  min-width: 80px;
  padding: 10px 0;
}

.flowercalendar {
  position: fixed;
  right: 0px;
  top: 160px;
  z-index: 999;
}

.navSoft_outer {
  position: fixed;
  bottom: 60px;
  // height: 50px;
  z-index: 1000000;
  width: 100%;
  text-align: center;
  background: #fff;

  .dd_map_item {
    padding: 10px 0;
  }
}

/* 音频启用按钮样式 */
.dd_map_audio {
  position: relative;
  animation: pulse 2s infinite;
}

.dd_map_audio .audio-icon {
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.dd_map_audio .audio-tip {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4444;
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 8px;
  line-height: 1;
  animation: blink 1s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}
</style>
