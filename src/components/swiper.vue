<template>
    <div>
        <!-- <el-carousel
          :interval="4000"
          type="card"
          :autoplay="false"
          arrow="never"
          indicator-position="none"
          :initial-index="1"
        >
          <el-carousel-item v-for="(item, index) in imgList" :key="index" :id="item.id">
            <div class="choice-box">
                <div :class="item.secondTypeId == 1 ? 'dd_recommend dd_recommend_play' : item.secondTypeId == 4 ? 'dd_recommend dd_recommend_ticket' : item.secondTypeId == 5 ? 'dd_recommend dd_recommend_ticketpackage' : item.secondTypeId == 3 ? 'dd_recommend dd_recommend_activity' : item.secondTypeId == 2 ? 'dd_recommend dd_recommend_line' : 'dd_recommend'">
                    <img :src="item.img" alt="">
                    <div class="dd_recommend_entrance">
                        <img src="https://www.dreamdeck.cn:10443/app/icons/greenheart/entrance.png" alt="">
                    </div>
                </div>
            </div>
          </el-carousel-item>
        </el-carousel> -->
        <swiper
            :options="swiperOption"
            ref="mySwiper"
            class="swiper"
            v-if="imgList.length > 0"
            @click-slide="videoPlay"
            @transitionEnd="onSwiper"
        >
            <swiper-slide
            class="list-item"
            v-for="(item, index) in imgList"
            :key="index"
            
            >
                <div class="choice-box">
                    <div :class="item.secondTypeId == 1 ? 'dd_recommend dd_recommend_play' : item.secondTypeId == 4 ? 'dd_recommend dd_recommend_ticket' : item.secondTypeId == 5 ? 'dd_recommend dd_recommend_ticketpackage' : item.secondTypeId == 3 ? 'dd_recommend dd_recommend_activity' : item.secondTypeId == 2 ? 'dd_recommend dd_recommend_line' : 'dd_recommend'">
                        <img :src="item.img" alt="">
                        <div class="dd_recommend_entrance">
                            <img src="https://www.dreamdeck.cn:10443/app/icons/greenheart/entrance.png" alt="">
                        </div>
                    </div>
                </div>
            </swiper-slide>
      </swiper>
    </div>
</template>

<script>
    export default {
        props: ['imgList'],
        data(){
            return{ 
                swiperOption: {
                    initialSlide: 0, //设定初始化时slide的索引
                    effect: 'coverflow',
                    slidesPerView: 1.1,
                    spaceBetween: 20,
                    centeredSlides: true, //活动块会居中，而不是默认状态下的居左
                    loop: true,
                    speed: 1000,
                    // autoplay: {
                    //     delay: 5000,
                    //     stopOnLastSlide: false,
                    //     disableOnInteraction: false,
                    // },
                    observer: true, //修改swiper自己或子元素时，自动初始化swiper
                    observeParents: true, //修改swiper的父元素时，自动初始化swiper
                    coverflowEffect: {
                        rotate: 28, // slide做3d旋转时Y轴的旋转角度。默认50。
                        stretch: 75, // 每个slide之间的拉伸值，越大slide靠得越紧。
                        depth: 1000, // slide的位置深度。值越大z轴距离越远，看起来越小。
                        modifier: 1, // depth和rotate和stretch的倍率，相当于depth*modifier、rotate*modifier、stretch*modifier，值越大这三个参数的效果越明显。默认1。
                        slideShadows: false, // 开启slide阴影。默认 true。
                    },
                },
            }
        },
        methods : {
            // swiper 滑动触发
            onSwiper(swiper){
                console.log(swiper, this.$refs.mySwiper.swiperInstance.activeIndex);
                let activeIndex = this.$refs.mySwiper.swiperInstance.activeIndex
                //将点击时获取的的banner图的序列号转为数组的索引值
                let inx = activeIndex - 2  //  因swiper插件渲染的时候会比传入的数据多两个
                // //当序列号是0的时候判为数组的最后一位
                if(activeIndex == 1 ){
                    inx = this.$props.imgList.length - 1
                //当序列号是大于数组1的时候判为数组的第一位
                }else if(activeIndex == this.$props.imgList.length + 2){
                    inx = 0
                }
                console.log(inx);
                //取数组中的值
                let i = this.$props.imgList[inx]
                this.$emit('updateSpinning', i)
            },
            // swiper 点击触发
            videoPlay(list){
                console.log(list);
                //将点击时获取的的banner图的序列号转为数组的索引值
                let inx = list - 2  //  因swiper插件渲染的时候会比传入的数据多两个
                // //当序列号是0的时候判为数组的最后一位
                if(list == 1 ){
                    inx = this.$props.imgList.length - 1
                //当序列号是大于数组1的时候判为数组的第一位
                }else if(list == this.$props.imgList.length + 2){
                    inx = 0
                }
                console.log(inx);
                //取数组中的值
                let i = this.$props.imgList[inx]
                // console.log(i, this.$props.imgList)
                this.picStartGame(i)
            },
            // 传递参数
            picStartGame(item){
                console.log(item, item.img);
                // alert(1)
                this.$emit('checked', item)
            }
        }
    }
    
</script>

<style scoped>
    .swiper, .choice-box{
        height: 135px;
        
    }

    .dd_recommend{
        width: 197px;
        height: 135px;
        margin: auto;
        border-radius: 10px;
        overflow: hidden;
        position: relative;
        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
    }

    .dd_recommend>img{
        width: 100%;
        height: 100%;
    }

    
    .dd_recommend_play::after{
        width: 52px;
        height: 26px;
        text-align: center;
        line-height: 26px;
        display: block;
        content: '游乐';
        color: #fff;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 99;
        background: #6392FF;
        border-radius: 0 0 10px 0;
    }

    .dd_recommend_line::after{
        width: 52px;
        height: 26px;
        text-align: center;
        line-height: 26px;
        display: block;
        content: '游线';
        color: #fff;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 99;
        background: #FFBC21;
        border-radius: 0 0 10px 0;
    }

    .dd_recommend_activity::after{
        width: 52px;
        height: 26px;
        text-align: center;
        line-height: 26px;
        display: block;
        content: '活动';
        color: #fff;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 99;
        background: #FF3E3E;
        border-radius: 0 0 10px 0;
    }

    .dd_recommend_ticket::after{
        width: 52px;
        height: 26px;
        text-align: center;
        line-height: 26px;
        display: block;
        content: '门票';
        color: #fff;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 99;
        background: #679F6A;
        border-radius: 0 0 10px 0;
    }

    .dd_recommend_ticketpackage::after{
        width: 52px;
        height: 26px;
        text-align: center;
        line-height: 26px;
        display: block;
        content: '联票';
        color: #fff;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 99;
        background: #679F6A;
        border-radius: 0 0 10px 0;
    }

    .dd_recommend_entrance{
        width: 33px;
        height: 20px;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 10px;
        position: absolute;
        right: 7px;
        bottom: 9px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .dd_recommend_entrance img{
        width: 9px;
        height: 8px;

    }

    .el-carousel--horizontal {
        /* width: 910px; */
        border: none;
    }
    .el-carousel .el-carousel__item .el-carousel__mask {
        background: none;
    }
</style>
