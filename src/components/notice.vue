<!-- 公告栏组件 -->
<template>
    <div class="notice-bar" @click="tipClick">
        <!-- <div class="notice-bar__icon">
            <img src="../assets/images/patient/homepage/tip.png">
        </div> -->
        <div ref="wrap" class="notice-bar__wrap">
            <div ref="content" class="notice-bar__content" :style="contentStyle">{{ text }}</div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'NoticeBar',
    props: {
        text: {
            type: String,
            default: ''
        },
        speed: {
            type: Number,
            default: 50
        },
        defaultWidth: {
            type: Number,
            default: 400
        }
    },
    data () {
        return {
            contentStyle: {
                transitionDuration: '0s',
                transform: 'translateX(0px)'
            },
            wrapWidth: 0,
            contentWidth: 0,
            time: 0,
            timer: null,
            convertSpeed: 1
        }
    },
    created () {},
    mounted () {
        if (this.text) {
            this.init()
        }
    },
    watch: {
        text (val) {
            this.init()
        }
    },
    methods: {
        init () {
            const _width = window.innerWidth
            this.convertSpeed = _width / this.defaultWidth * this.speed  // 根据分辨率转化成不同的速度
            this.wrapWidth = this.$refs.wrap.offsetWidth
            
            this.startAnimate()
            this.timer = setInterval(() => {
                this.startAnimate()
                console.log(1);
            }, this.time * 1000)
            this.$once('hook:beforeDestroy', () => {
                clearInterval(this.timer)
                this.timer = null
            })
        },
        startAnimate () {
            
            this.contentStyle.transitionDuration = '0s'
            this.contentStyle.transform = 'translateX(' + this.wrapWidth + 'px)'
            this.time = this.wrapWidth / this.convertSpeed
            setTimeout(() => {
                
                this.contentWidth = this.$refs.content.offsetWidth
                console.log(this.contentWidth);
                this.contentStyle.transitionDuration = this.time + 's'
                this.contentStyle.transform = 'translateX(-' + this.contentWidth + 'px)'
                
            }, 200)
        },
        tipClick () {
            this.$emit('click')
        }
    }
}
</script>
<style scoped>
    .notice-bar {
        position: relative;
        width: 100%;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #868DAA;
        display: flex;
        align-items: center;
    }
    .notice-bar .px2rem{
        height: 80px;
        padding-left: 0;
        padding-right: 0;
        font-size: 28px;
    }

    .notice-bar .notice-bar__icon img{
        width: 100%;
    }

    .notice-bar .notice-bar__icon .px2rem{
        width: 56px;
        height: 28px;
        margin-right: 20px;
    }
    .notice-bar .notice-bar__wrap {
        position: relative;
        display: flex;
        flex: 1;
        height: 100%;
        align-items: center;
        overflow: hidden;
        
    }
    .notice-bar .notice-bar__wrap .notice-bar__content {
        position: relative;
        white-space: nowrap;
        transition-timing-function: linear;
        color: #313131;
        line-height: 26px;
        font-size: 12px;
    }
    
</style>

