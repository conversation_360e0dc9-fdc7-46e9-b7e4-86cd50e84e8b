<template>
  <div class="container">
    <!-- 对话记录 -->
    <div class="log" :class="logList.length > 1 ? 'first' : ''">
      <div class="item" v-for="(item, index) in logList" :key="index">
        <span>{{ item.text }}</span>
      </div>
    </div>
    <!-- AI对话 -->
    <div class="chat">
      <div class="input-container">
        <img src="@/assets/icon/audio.png" mode="scaleToFill" @click="switChAudioInput" class="audio" />
        <input v-show="!audioInput" v-model="inputVal" type="text" class="input" placeholder="有什么问题尽管问我">
        <!-- 长按录音 -->
        <div v-show="audioInput" class="audio-input" @touchstart="startRecorded" @touchend="stopRecorded"
          @mousedown="startRecorded" @mouseup="stopRecorded">按住 说话</div>
      </div>
      <div class="send" @click="send">发送</div>
    </div>
  </div>
</template>

<script>
import { getWxAuth } from "@/api/operationmini"
export default {
  name: '',
  components: {},
  props: {
    msg: {
      type: String,
      default: ''
    }
  },
  data: () => ({
    API_KEY: '75c8e75467b8b4b87df8d48b6c87f822',
    API_SECRET: 'YzIzMTVjMjU2YThlM2UwOTUzMjgzZTNh',
    APPID: '941233d7',
    logList: [],
    inputVal: '',
    audioInput: false
  }),
  computed: {},
  watch: {
    '$props.msg': {
      handler(val) {
        if (val != '') {
          this.logList.push({ text: val, isRight: false })
        }
      }
    },
    'logList': {
      handler(val) {
        if (val.length > 2) {
          val.shift()
        }
      }
    }
  },
  async created() {
    document.οncοntextmenu = function (e) {
      e.preventDefault();
    };
    const configParams = await getWxAuth({
      appId: 'wxf499be1483b759e1',
      url: window.location.href,
    });
    console.log('configParams', configParams);
    this.$wx.config({
      debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
      appId: "wxf499be1483b759e1", // 必填，公众号的唯一标识
      timestamp: configParams.data.timestamp, // 必填，生成签名的时间戳
      nonceStr: configParams.data.noncestr, // 必填，生成签名的随机串
      signature: configParams.data.signature, // 必填，签名
      jsApiList: [
        "startRecord",
        "stopRecord",
        "translateVoice",
      ], // 必填，需要使用的JS接口列表
    });
  },
  mounted() {
  },
  methods: {
    // 开始录制语音条
    async startRecorded() {
      this.$wx.ready(() => {
        this.$wx.startRecord()
      })
    },
    // 停止录制语音条
    async stopRecorded() {
      const self = this
      this.$wx.ready(() => {
        // this.recorder.stop();
        // console.log('停止录音', this.inputVal)
        self.$wx.stopRecord({
          success: function (res) {
            var localId = res.localId;
            self.$wx.translateVoice({
              localId: localId, // 需要识别的音频的本地Id，由录音相关接口获得
              isShowProgressTips: 1, // 默认为1，显示进度提示
              success: function (res) {
                console.log(res.translateResult); // 语音识别的结果
                self.inputVal = res.translateResult;
                self.send()
              }
            });
          }
        });
      })

    },
    switChAudioInput() {
      this.audioInput = !this.audioInput
    },
    send() {
      if (this.inputVal.trim() == '') return;
      console.log(this.inputVal);
      this.$emit('send', this.inputVal)
      this.logList.push({ text: this.inputVal, isRight: true })
      if (this.logList.length > 2) this.logList.shift()
      this.inputVal = ''
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  position: absolute;
  bottom: 60px;
  left: 0;
  width: calc(100% - 20px);
  z-index: 99;
  padding: 10px;
  opacity: 0.7;

  .chat {
    display: flex;
    justify-content: space-between;
    height: 36px;

    .input-container {
      background: #EBFCF2;
      border-radius: 465px;
      display: flex;
      align-items: center;
      padding: 0 12px;
      width: calc(100% - 88px - 20px - 12px);
      border: #053829a1 1px solid;

      .audio {
        width: 13px;
        height: auto;
      }

      .input {
        background-color: transparent;
        border: none;
        padding-left: 12px;
        height: 36px;
        width: calc(100% - 12px);

        &:focus-visible {
          outline: none;
        }
      }

      .audio-input {
        width: calc(100% - 12px);
        display: flex;
        align-items: center;
        justify-content: center;
        user-select: none;
        margin-left: 20px;
        height: 90%;
        border-radius: 10px;
        user-select: none;
        -webkit-touch-callout: none;
        /*系统默认菜单被禁用*/
        -webkit-user-select: none;

        /*webkit浏览器*/
        &:active {
          background: rgb(12, 164, 121);

        }
      }
    }

    .send {
      background: #0D7456;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;
      height: 36px;
      width: 88px;
      border-radius: 465px;
    }
  }

  .log {
    height: calc(100% - 36px);
    overflow: hidden;

    .item {
      width: fit-content;
      border-radius: 10px;
      padding: 8px 20px;
      margin-left: 2px;
      background: #EBFCF2;
      box-shadow: 0px 0px 10px 0px rgba(5, 104, 132, 0.1);
      margin-bottom: 6px;
      color: #053829;
      position: relative;
      max-width: calc(100% - 20px - 20px - 20px);
      overflow: hidden;
      word-wrap: break-word;

      &:last-of-type {
        border: 1px solid rgba(13, 116, 86, 0.6);
      }
    }
  }

  .first {
    .item {
      &:first-of-type {
        background: linear-gradient(180deg, rgba(216, 216, 216, 0) 41%, #EBFCF2 100%);
        color: #05382981;

        &::before {
          content: '';
          position: absolute;
          top: -1px;
          left: -1px;
          right: -1px;
          bottom: -1px;
          border-radius: 11px;
          background: linear-gradient(to top, rgb(13 116 86 / 34%), rgba(255, 255, 255, 0));
          z-index: -1;
        }

      }
    }

  }
}
</style>
