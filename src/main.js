/*
 * @Author: sunfeiyue <EMAIL>
 * @Date: 2023-12-21 12:05:23
 * @LastEditors: sunfeiyue <EMAIL>
 * @LastEditTime: 2023-12-22 15:51:49
 * @FilePath: \dd-mini-operation-saas-park-map\src\main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import wx from 'weixin-js-sdk';
import VueAMap from 'vue-amap'
// import vueSwiper from 'vue-awesome-swiper'
// import 'swiper/css/swiper.css'
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
// import VueJsonp from 'vue-jsonp'
import "../src/assets/font/text.css";
// import MyWorkletProcessor from "@/class/my-worklet-processor.js"
Vue.use(ElementUI);
Vue.use(VueAMap)
// Vue.use(VueJsonp)
import Vconsole from 'vconsole'
new Vconsole()
Vue.config.productionTip = false

// registerProcessor('my-worklet-processor', MyWorkletProcessor);
VueAMap.initAMapApiLoader({
  key: '4c14d29ba1e84afdfbce1ad4191fc77e',
  plugin: ['AMap.Riding', 'AMap.OverView', 'AMap.ToolBar', 'AMap.MapType', 'AMap.PlaceSearch', 'AMap.Geolocation', 'AMap.Geocoder', 'AMap.GeometryUtil', 'AMap.DistrictSearch', 'AMap.MarkerClusterer'],
  v: '1.4.4',
  uiVersion: '1.0'
})
new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')

Vue.prototype.$wx = wx;