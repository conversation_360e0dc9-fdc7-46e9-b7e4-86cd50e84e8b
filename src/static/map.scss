.dd_map {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  z-index: 998;
}
#dd_map_container {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 1;
}
// 搜索分类全部
.all {
  width: 90%;
  height: 138px;
  opacity: 1;
  position: absolute;
  z-index: 999;
  margin-top: 10px;
  // margin: 0 auto;
  left: 50%;
  transform: translateX(-50%);
  // border: 1px solid #FFFFFF;
}
.dd_map_tab_type {
  width: 100%;
  height: 56px;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 10px 0;
  box-sizing: border-box;
  z-index: 9999;
}
.dd_map_tab_type_back {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  /* background: linear-gradient(180deg, #0BB977 0%, rgba(11, 185, 119, 0.7) 99%); */
  background: linear-gradient(180deg, #4fca7c 0%, rgba(117, 219, 173, 0.8) 99%);
  /* border-bottom: 1px solid #fff; */
}
.map_outer {
  width: 100%;
  height: 100%;
  position: relative;
}
.dd_map_tab_type_back > div {
  width: 100%;
  height: 1px;
  position: absolute;
  left: 0;
  bottom: -1px;
  background: linear-gradient(
    270deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 1) 48%,
    rgba(255, 255, 255, 0) 100%
  );
}
.dd_map_tab_type_absolute {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 999;
  padding: 0 20px;
  box-sizing: border-box;
}
.dd_map_tab_type_absolute >>> .swiper-container,
.dd_map_tab_type_absolute >>> .swiper-container .swiper-wrapper,
.dd_map_tab_type_absolute >>> .swiper-container .list-item {
  width: 100% !important;
}
.dd_map_tab_type_absolute_doot {
  width: 60px;
  position: absolute;
  bottom: 4px;
  left: 50%;
  margin: 0 0 0 -30px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #2020205f;
  border-radius: 30px;
}
.dd_map_tab_type_absolute_doot div {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: #ff6d00;
  margin: 0 3px;
  transform: all 1s;
}
.dd_map_tab_type_absolute_doot .dd_map_tab_type_absolute_doot_select {
  /* width: 15px; */
  background: #ffffff;
  border: 0.5px solid rgba(255, 109, 0, 0.6);
  box-sizing: border-box;
  border-radius: 30px;
  transform: all 1s;
}
.dd_map_tab_type_flex {
  display: flex;
  align-items: center;
  /* justify-content: space-between; */
  flex: 1;
  overflow: auto;
  height: 100%;
}
/* .dd_map_tab_type_flex::-webkit-scrollbar{  
        display: none; 
    } */
.dd_map_tab_type_all {
  opacity: 1;
}
.dd_map_tab_type_flex_list {
  margin: 0 15px;
  height: 100%;
  padding: 4px 0 0 0;
  box-sizing: border-box;
}
.dd_map_tab_type_flex > .dd_map_tab_type_flex_list:nth-of-type(1) {
  margin-left: 0;
}
.dd_map_tab_type_absolute > .dd_map_tab_type_flex_list {
  margin-right: 0;
}
.dd_map_tab_type_flex_list_opacity {
  opacity: 1 !important;
  position: relative;
}

.dd_map_tab_type_flex_list_opacity::after {
  width: 100%;
  height: 3px;
  background: #fff;
  border-radius: 60px;
  display: block;
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
}

.dd_map_tab_type_flex_list_opacity p {
  /* color: #00B150 !important; */
  /* text-shadow: #00B04F 1px 0px 0px; */
  font-weight: bold;
  color: #fff !important;
}

.dd_map_tab_type_flex_list_opacity .dd_map_tab_type_flex_list_img {
  position: relative;
  border: 2px solid #fff;
  border-radius: 8px;
  box-sizing: border-box;
}

.dd_map_tab_type_flex_list_img {
  width: 30px;
  height: 30px;
  display: block;
  margin: auto;
  box-sizing: border-box;
  padding: 2px;
}
.dd_map_tab_type_flex_list img,
.dd_map_tab_type_all img {
  width: 100%;
  height: 100%;
}
.dd_map_tab_type_flex_list p,
.dd_map_tab_type_all p {
  color: #333333;
  text-align: center;
  font-size: 12px;
  /* margin: 2px 0 0 0; */
  width: 100%;
  white-space: nowrap;
  white-space: nowrap;
  max-width: 70px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.guide {
  position: absolute;
  z-index: 999;
  width: 100%;
  height: 100vh;
  // background: radial-gradient(circle at 370px 30px, transparent 20px, rgba(44, 44, 44, 0.6) 21px);
  // background: rgba(44, 44, 44, 0.6); 
  .guide>div{
    overflow:hidden; /* to contain the child's blue box shadow */
    color:#fff;
  }

  .shadow-aside{
    overflow: hidden;
    position: relative;

    // 金刚区
    &.guide_classify{
      width: 100%;
      height: 40vh;
      img{
        position: absolute;
        z-index: 999;
        top: 182px;
        height: 38px;
        left: 50%;
        transform: translateX(-50%);
      }
    }

    // 手势
    &.guide_hand{
      width: 100%;
      height: 15vh;
      img{
        position: absolute;
        z-index: 999;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        height: 80%;
      }
    }
    &.guide_music{
      width: 100%;
      height: calc(5vh + 53px);
      img{
        position: absolute;
        z-index: 999;
        top: 10px;
        right: 50px;
        width: 50%;
      }
    }
    &.guide_line{
      width: 100%;
      height: calc(40vh - 37px);
      img{
        position: absolute;
        z-index: 999;
        top: 32px;
        right: 54px;
        width: 50%;
      }
    }
  }

  .shadow-hole{
    box-shadow: 0 0 0 100vh rgba(44,44,44,0.6);  /* huge blue box shadow */
    position: absolute;
    &.guide_classify-hole{
      height: 175px;
      border-radius: 8px;
      opacity: 1;
      top: 6px;
      left: 50%;
      transform: translateX(-50%);
      width: 94%;
    }
    &.guide_hand-hole{
      width: 0;
      height: 0;
    }
    &.guide_music-hole{
      bottom: 10px;
      width: 46px;
      height: 46px;
      right: 17px;
      border-radius: 50%;
    }
    &.guide_line-hole{
      right: 17px;
      width: 46px;
      height: 46px;
      top: 0px;
      border-radius: 50%;
    }
  }

  .hand {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50%;
    height: 7.94%;
    // z-index: 1000;
    img {
      width: 100%;

      // height: 100%;
    }
  }
  .guide_rightbutton {
    position: fixed;
    z-index: 99;
    right: 20px;
    bottom: 100px;
    width: 100%;
    height: 160px;
    overflow: hidden;
    img{
    // z-index: 1000;

    }
    .guidemusic > div {
      position: fixed;
      z-index: 99;
      right: 20px;
      width: 40px;
      height: 40px;
      bottom: 212px;

      border-radius: 50%;
    box-shadow: 0 0 0 100vh rgba(0,0,0,0.4);  /* huge blue box shadow */

      // box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
      // background: #d8d8d8;
    }
    .guideline > div {
      position: fixed;
      z-index: 99;
      right: 20px;
      width: 40px;
      height: 40px;
      bottom: 156px;

      border-radius: 50%;
    box-shadow: 0 0 0 100vh rgba(0,0,0,0.4);  /* huge blue box shadow */

      // box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
      // background: #d8d8d8;
    }
    .guidemusic > img {
      height: 53px;
      position: fixed;
      bottom: 192px;
      right: 60px;
    }
    .guideline > img {
      height: 36px;
      position: fixed;
      right: 55px;
      bottom: 133px;
    }
  }
}
.onlyserach {
  margin: 10px 20px;
  position: fixed;
  top: 10px;
  left: 9px;
}

// 搜索
.search {
  display: flex;
  width: 100%;
  height: 30px;
  border-radius: 8px;
  opacity: 1;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.5);

  box-sizing: border-box;
  border: 1px solid rgba(255, 255, 255, 0.7);
  position: relative;
  z-index: 999;
  backdrop-filter: blur(10px);
  div:nth-child(2) {
    // flex: 1;
    width: 54%;
  }
  input {
    border: 0;
    background: none;
    outline: none;
    display: inline-block;
    height: 100%;
    width: 100%;
    flex: 1;
    font-family: Source Han Sans CN;
    font-size: 12px;
    font-weight: 350;
    line-height: 17px;
    letter-spacing: 0px;

    color: #666666;
  }

  img {
    width: 18px;
    height: 18px;
    margin-left: 19px;
    margin-top: 5px;
  }
  .search_img {
    position: relative;
    // width: 10%;
  }
  .search_img::after {
    width: 2px;
    background: #ffffff;
    height: 20px;
    content: "";
    display: block;
    right: -15px;
    top: 15%;
    // width: 100%;
    position: absolute;
  }
}
.classify {
  width: 100%;
  height: 135px;
  overflow: auto;
  border-radius: 0 0 8px 8px;
  opacity: 1;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  position: relative;
  margin: -5px 0;
  padding: 15px 10px 0px 10px;
  box-sizing: border-box;
  .class-slide-outer{
    width: 100%;
    overflow: auto;
    padding-bottom: 15px;
  }
  .class-slide{
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    div {
      width: 16.6666666%;
      text-align: center;
      font-family: Source Han Sans CN;
      font-size: 12px;
      font-weight: normal;
      line-height: 17px;
      height: 56px;
      letter-spacing: 0px;
      box-sizing: border-box;
      color: #333333;
      

      img {
        width: 26px;
        height: 26px;
        display: block;
        margin: 0 auto;
        padding-top: 5px;
      }
    }
    div:nth-child(1)::after {
      width: 0px;
      background: #ffffff;
      height: 26px;
      content: "";
      display: block;
      top: 18px;
      left: 15%;
      // width: 100%;
      position: absolute;
    }
    // 收起之前的竖杠
    .classify_notshow::before {
      width: 0px;
      background: #ffffff;
      height: 26px;
      content: "";
      display: block;
      top: 80px;
      right: 15%;
      position: absolute;
    }
    .classify_checked {
      // width: 48px;
      // height: 58px;
      border-radius: 4px;
      opacity: 1;
      // padding-top: 3px;
      background: rgba(153, 153, 153, 0.3);
  
      box-sizing: border-box;
      // border: 1px solid rgba(255, 255, 255, 0.5);
    }
  }

}

.search button {
  width: 57px;
  height: 28px;
  border-radius: 7px;
  opacity: 1;
  border: 0;
  background: #81d99e;
  color: #fff;
}
.dd_map_search {
  width: calc(100% - 18px);
  position: fixed;
  top: 10px;
  left: 9px;
  // border: 1px solid #ffffff;
  // border-radius: 460px;
  // background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 12px;
  box-sizing: border-box;
}
.dd_map_search_flex {
  flex: 1;
  display: flex;
  align-items: center;
}
.dd_map_search_flex img {
  width: 25px;
  height: 25px;
  filter: brightness(0.8);
}
.dd_map_search_flex input {
  flex: 1;
  display: block;
  height: 25px;
  margin: 0 0 0 7px;
  border: none !important;
  background: none !important;
  outline: none;
  color: rgba(55, 203, 123, 0.5);
}
.dd_map_search_flex input::placeholder {
  color: rgba(55, 203, 123, 0.5);
}
.dd_map_search p {
  font-size: 14px;
  color: #37cb7b;
  margin: 0 0 0 10px;
}
.dd_map_content {
  width: 100%;
  height: 100vh;
  position: absolute;
  left: 0;
  /* top: 80px; */
  background: linear-gradient(180deg, #c5f0e2 0%, rgba(255, 255, 255, 0.5) 4%),
    #ffffff;
  border-radius: 20px;
  z-index: 999999;
  border: 1px solid #fff;
  box-sizing: border-box;
}
.dd_map_content::after {
  display: block;
  content: "";
  width: 24px;
  height: 6px;
  position: absolute;
  left: 50%;
  top: 6px;
  margin: 0 0 0 -12px;
  // background: url("https://www.dreamdeck.cn:10443/app/icons/southtwoloop/saas_arrows.png")
  //   no-repeat 100%/100%;
}
.dd_map_content_type {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 16px 0 0 0;
  box-sizing: border-box;
  margin: 0 0 20px 0;
}
.dd_map_content_type_list {
  width: 20%;
  margin: 4px 0 0 0;
}
.dd_map_content_type_list > img {
  width: 52px;
  height: 52px;
  display: block;
  margin: auto;
}
.dd_map_content_type_list > p {
  font-size: 13px;
  color: #505050;
  text-align: center;
  margin: 3px 0 0 0;
}
.dd_map_content_gather {
  /* min-height: 100vh; */
  height: 76vh;
  padding: 13px 10px;
  box-sizing: border-box;
  /* background: linear-gradient(178deg, #D4FCFC 1%, #F7F9F9 45%, #F7F9F9 98%); */
  border-radius: 20px 20px 0 0;
  /* border-image: linear-gradient(180deg, #FFFFFF 0%, rgba(255,255,255,0.00) 100%) ; */
}

.dd_map_content_gather_flex {
  display: flex;
  align-items: center;
}
.dd_map_content_gather_flex p {
  color: #666666;
  font-size: 16px;
  margin: 0 20px 0 0;
}
.dd_map_content_gather_flex p:nth-last-of-type(1) {
  margin: 0;
}
.dd_map_content_gather_flex_pitch {
  font-size: 18px !important;
  color: #333333 !important;
  position: relative;
  z-index: 99;
}

.dd_map_content_gather_ticket {
  /* height: 20vh;
        overflow: hidden; */
  max-height: 400px;
  overflow: auto;
  padding: 0 0 62px 0;
  box-sizing: border-box;
}
.dd_map_content_gather_ticket_list {
  width: 100%;
  border-radius: 10px;
  box-shadow: 0px 8px 20px 0px rgba(0, 0, 0, 0.04);
  border: 1px solid #ffffff;
  background: rgba(255, 255, 255, 0.7);
  padding: 15px 10px;
  box-sizing: border-box;
  display: flex;
  margin: 10px 0 0 0;
}

.dd_map_content_gather_ticket_list > img {
  width: 70px;
  height: 70px;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  margin: 0 10px 0 0;
}
.dd_map_content_gather_ticket_list_center_title {
  display: flex;
  align-items: center;
}
.dd_map_content_gather_ticket_list_center_title > p:nth-of-type(1) {
  color: #3d3d3d;
  font-size: 14px;
  font-weight: 500;
}
.dd_map_content_gather_ticket_list_center_title > p:nth-of-type(2) {
  color: #3d3d3d;
  font-size: 14px;
}
.dd_map_content_gather_ticket_list_center_label {
  display: flex;
  align-items: center;
}
.dd_map_content_gather_ticket_list_center_label p {
  font-size: 12px;
  color: #999999;
  border-radius: 2px;
  border: 0.2px solid #999999;
  padding: 2px 6px;
  box-sizing: border-box;
}
.dd_map_content_gather_ticket_list_center_money {
  display: flex;
  align-items: center;
  margin: 10px 0 0 0;
}
.dd_map_content_gather_ticket_list_center_money span {
  font-size: 16px;
  color: #fc3e5a;
}
.dd_map_content_gather_ticket_list_center_money span:nth-of-type(1) {
  font-size: 12px;
}
.dd_map_content_gather_ticket_list_button {
  flex: 1;
  display: flex;
  justify-content: right;
  align-items: flex-end;
}
.dd_map_content_gather_ticket_list_button p {
  display: inline-block;
  width: 67px;
  height: 32px;
  border-radius: 4px;
  font-size: 16px;
  line-height: 32px;
  text-align: center;
  background: #ff7d20;
  color: #fff;
}

.dd_map_floor {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 70px;
  z-index: 9999999;
  padding: 0px 40px 7px 40px;
  box-sizing: border-box;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.12);
}
.dd_map_floor img {
  width: 20px;
  height: 20px;
  display: block;
  margin: auto;
}
.dd_map_floor p {
  margin: 5px 0 0 0;
  font-size: 12px;
  color: #333;
}
.dd_map_floor > div:nth-of-type(2) p,
.dd_map_floor > div:nth-of-type(3) p {
  font-size: 12px;
  color: #666;
}

.dd_map_right_position {
  position: absolute;
  right: 20px;
  top: -180px;
  z-index: 9999;
  opacity: 1;
  transition: all 0.5s;
}

.dd_map_right_position_all {
  opacity: 0;
}

.dd_map_wire {
  background: #fff;
  border-radius: 12px;
  width: 40px;
  height: 40px;
  box-shadow: 0px 8px 20px 0px rgba(0, 0, 0, 0.3);
  box-sizing: border-box;
  /* position: fixed;
        left: 10px;
        bottom: 160px; */
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: center;
  margin: 0 0 16px 0;
}
.dd_map_wire > img {
  width: 24px;
  height: 24px;
  display: block;
  margin: auto;
  filter: brightness(0.3);
}
.dd_map_wire > p {
  color: #3d3d3d;
  font-size: 12px;
  text-align: center;
  margin: auto;
}
/* poi 详情 */
.dd_map_particulars {
  position: fixed;
  left: 2%;
  bottom: 72px;
  width: 96%;
  /* overflow-y: auto; */
  background: #fff;

  box-sizing: border-box;
  border-radius: 10px;
  z-index: 99999;
}

.dd_map_particulars_facility {
  text-align: center;
  padding: 10px 5px;
  box-sizing: border-box;
}

.dd_map_particulars_facility p {
  padding: 10px 0;
  box-sizing: border-box;
  font-size: 12px;
}

.dd_map_particulars_facility p:nth-of-type(2) {
  border-top: 1px solid #03bfc5;
}

.dd_map_particulars_flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 17px;
  box-sizing: border-box;
  border-top: 1px solid #d8d8d8;
}
.dd_map_particulars_flex > div {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}
.dd_map_particulars_flex > div img {
  width: 18px;
  height: 18px;
}
.dd_map_particulars_flex > div p {
  font-size: 14px;
  color: #021408;
  margin: 0 0 0 5px;
}
/* .dd_map_particulars_height{
        height: 120px !important;
    } */
.dd_map_particulars_poi {
  display: flex;
  /* align-items: center; */
}
.dd_map_particulars_poi > img {
  width: 70px;
  height: 70px;
  border-radius: 14px;
}
.dd_map_particulars_top {
  display: flex;
  align-items: center;
  padding: 20px 20px 0 20px;
  box-sizing: border-box;
}
.dd_map_particulars_top_one {
  flex: 1;
}
.dd_map_particulars_audio {
  margin: 0 0 0 14px;
}
.dd_map_particulars_audio img {
  width: 40px;
  height: 40px;
}
.dd_map_particulars_audio p {
  font-size: 12px;
  text-align: center;
  color: #999999;
}
.dd_map_particulars_address {
  display: flex;
  align-items: center;
  margin: 10px 0;
}
.dd_map_particulars_address img {
  width: 11px;
  height: 15px;
}
.dd_map_particulars_address p {
  font-size: 12px;
  color: #999;
  margin: 0 0 0 7px;
}
.dd_map_particulars_poi_text {
  flex: 1;
  margin: 0 0 0 10px;
}
.dd_map_particulars_poi_text > p:nth-of-type(1) {
  font-size: 14px;
  color: #1a1a1a;
}
.dd_map_particulars_poi_text > p:nth-of-type(2) {
  font-size: 12px;
  line-height: 20px;
  color: #999999;
  margin: 8px 0 0 0;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  height: 40px;
}
.dd_map_particulars_poi_button {
  height: 40px;
  font-size: 14px;
  color: #fff;
  padding: 10px 20px;
  box-sizing: border-box;
  background: #679f6a;
  border-radius: 14px;
}
.dd_map_particulars_introduce {
  margin: 16px 0 0 0;
  font-size: 10px;
  line-height: 18px;
  color: #313131;
}

.dd_map_content_gather_tent_list {
  width: 100%;
  height: 180px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  margin: 20px 0;
}
.dd_map_content_gather_tent_list > img {
  width: 100%;
  height: 100%;
}
.dd_map_content_gather_tent_list > div {
  width: 100%;
  height: 52px;
  background: rgba(0, 0, 0, 0.4);
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 9;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-sizing: border-box;
}
.dd_map_content_gather_tent_list > div > div > p {
  color: #fff;
}
.dd_map_content_gather_tent_list > div > div > p:nth-of-type(1) {
  font-size: 16px;
}
.dd_map_content_gather_tent_list > div > div > p:nth-of-type(2) {
  font-size: 10px;
}
.dd_map_content_gather_tent_list > div > p {
  font-size: 12px;
  color: #fff;
}
.dd_map_content_gather_tent_list > div > p > span {
  font-size: 16px;
}

/* 大运河样式处理 */
.dd_map_tab_type_dyh .dd_map_tab_type_back {
  background: linear-gradient(180deg, #d9a059 0%, #edc4a3 100%) !important;
}
.dd_map_content_dyh {
  background: #fff !important;
}

.dd_map_tab_type_dyh .dd_map_tab_type_flex_list_opacity p {
  text-shadow: #ad7e2e 1px 0px 0px !important;
}

.dd_map_popup {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99999999;
  width: 100%;
  height: 100%;
  /* background: red; */
  display: flex;
  align-items: center;
  justify-content: center;
}
.dd_map_popup > div {
  width: 40%;
  padding: 20px 0;
  box-sizing: border-box;
  text-align: center;
  font-size: 16px;
  color: #fff;
  background: #000000b4;
  border-radius: 10px;
}
.dd_map_tab_type_notice {
  position: fixed;
  top: 70px;
  left: 50%;
  transform: translateX(-50%);
  padding: 0 10px;
  width: 90%;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: auto;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.7);
  border: 2px solid #ffffff;
  backdrop-filter: blur(30px);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.06);
  color: #4a4a4a;
  font-size: 0.75rem;
  z-index: 9999;
}
.dd_map_tab_type_notice > div:nth-of-type(1) {
  display: flex;
  align-items: center;
  width: 95%;
}
.dd_map_tab_type_notice_scroll {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  /* width: 55%; */

  /* animation-iteration-count: 1; */
}
.dd_map_tab_type_notice_scroll > div {
  -webkit-animation: 10s scroll linear infinite normal;
  animation: 10s scroll linear infinite normal;
}
@keyframes scroll {
  0% {
    -webkit-transform: translateX(90%);
  }

  100% {
    -webkit-transform: translateX(-120%);
  }
}
.dd_map_tab_type_notice > div:nth-of-type(1) > img {
  width: 18px;
  height: 17px;
  margin-right: 6px;
}
.dd_map_tab_type_notice_close {
  width: 11px !important;
  height: 10px !important;
}
.dd_map_top_flex {
  /* flex: 1; */
  // height: 48px;
  height: 28px;
  width: calc(100% - 80px);
  overflow: auto;
}
.dd_map_top {
  width: calc(100% - 40px);
  display: flex;
  align-items: center;
  position: fixed;
  left: 20px;
  top: 10px;
  z-index: 999;
}
.dd_map_top > img {
  width: 16px;
  height: 16px;
  // box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
  // border-radius: 14px;
}
.dd_map_top_flex input {
  color: #000;
  border: none;
  outline: none;
  width: 100%;
  height: 40px;
  background: none;
  font-size: 14px;
  padding: 0 10px;
  box-sizing: border-box;
}
.dd_map_top_flex_type {
  width: max-content;
  /* width: 100%; */
  height: 48px;
  /* display: flex; */
  align-items: center;
  justify-content: space-around;
  padding-top: 5px;
  box-sizing: border-box;
}
.dd_map_top_flex_type > div {
  width: 38px;
  height: 38px;
  text-align: center;
  line-height: 38px;
  font-size: 12px;
  color: #1a1a1a;
  background: #f7f7f7;
  border-radius: 50%;
  float: left;
  margin: 0px 5px;
}
.dd_map_top_flex_type .dd_map_top_flex_type_back {
  color: #fff;
  background: url("../assets/typechecked.png") no-repeat 100%/100%;
}
.dd_map_top_back {
  background: #ffffffa8;
  border-radius: 14px;
}

.dd_map_rightbutton {
  width: 40px;
  position: fixed;
  z-index: 99;
  right: 20px;
  // bottom: 100px;
  top: 60vh;
}

.dd_map_rightbutton div {
  width: 40px;
  height: 40px;
  margin: 0 0 16px 0;
  border-radius: 50%;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
}

.dd_map_rightbutton div:nth-last-child(1) {
  margin: 0;
}

.dd_map_rightbutton div img {
  width: 100%;
  height: 100%;
}
.clusterBubble {
  border-radius: 50%;
  color: #333;
  font-weight: 500;
  text-align: center;
  opacity: 0.88;
  background: #fff;
  box-shadow: 0 0 1px 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
  border: solid 2px #ffbc21;
  position: absolute;
  font-size: 14px;
  text-align: center;
  top: 0px;
  left: 0px;
  box-sizing: border-box;
}
.dd_map_particulars_navigation > div {
  color: #1a1a1a;
  font-size: 14px;
  text-align: center;
  margin: 10px 0;
  font-weight: 600;
}
.dd_map_particulars_navigation > div:nth-of-type(2) {
  margin: 40px 0 0 0;
}
.dd_map_arcar {
  height: 150px !important;
  white-space: nowrap;
  overflow: auto;
}
.dd_map_particulars,
.dd_map_arcar {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  // height: 145px;
  overflow-y: auto;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 14px 14px 0 0;
  z-index: 9999;
}

.dd_map_particulars_poi {
  display: flex;
  /* padding-top: 23px; */
}

.dd_map_particulars_poi > img {
  width: 70px;
  height: 70px;
  border-radius: 14px;
}

.dd_map_particulars_poi_text {
  flex: 1;
  margin: 0 0 0 10px;
}

.dd_map_particulars_poi_text > p:nth-of-type(1) {
  font-size: 14px;
  color: #1a1a1a;
}

.dd_map_particulars_poi_text > p:nth-of-type(2) {
  font-size: 10px;
  color: #679f6a;
  margin: 5px 0;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.dd_map_particulars_poi_text > p:nth-of-type(2) span {
  color: #1a1a1a;
}

.dd_map_particulars_poi_text > p:nth-of-type(3) {
  font-size: 10px;
  color: #1a1a1a;
}

.dd_map_particulars_poi_text > p:nth-of-type(3) span {
  color: #679f6a;
}

.dd_map_particulars_poi_text > p:nth-of-type(4) {
  font-size: 10px;
  color: #1a1a1a;
}

.dd_map_particulars_poi_button {
  height: 40px;
  font-size: 14px;
  color: #fff;
  padding: 10px 20px;
  box-sizing: border-box;
  background: #679f6a;
  border-radius: 14px;
}
/* 导航弹窗 */
.walkMessage {
  position: absolute;
  color: #333333;
  width: 305px;
  height: 59px;
  bottom: 85px;
  font-size: 14px;
  left: 50%;
  transform: translateX(-50%);
  opacity: 1;
  background: white;
  border-radius: 10px;
  border: 1px solid #d8d8d8;
  z-index: 99;
}
.walkMessage > div {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-left: 10px;
  padding-right: 10px;
  /* padding-top: 10px; */
}
.distance {
  margin-left: 10px;
}
.endnavigation {
  padding-right: 5px;
  border-right: 1px solid #d8d8d8;
  text-align: center;
  width: 30%;
  margin-right: 3px;
}
.endnavigation i {
  font-size: 22px;
}
.dd_map_wire {
  background: #fff;
  border-radius: 12px;
  width: 40px;
  height: 40px;
  box-shadow: 0px 8px 20px 0px rgba(0, 0, 0, 0.3);
  box-sizing: border-box;
  /* position: fixed;
            left: 10px;
            bottom: 160px; */
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: center;
  margin: 0 0 16px 0;
}
.dd_map_wire > img {
  width: 24px;
  height: 24px;
  display: block;
  margin: auto;
  /* filter: brightness(0.3); */
}
.dd_map_wire > p {
  color: #3d3d3d;
  font-size: 12px;
  text-align: center;
  margin: auto;
}
.dd_map_arcar_list {
  width: 80px;
  display: inline-block;
  margin: 0 15px 0 0;
}
.dd_map_arcar .dd_map_arcar_list:nth-last-child(1) {
  margin: 0;
}
.dd_map_arcar_list img {
  width: 100%;
  height: 80px;
  border: 1px solid #fff;
  border-radius: 23px;
  box-sizing: border-box;
}
.dd_map_arcar_list_select img {
  border: 3px solid #ffbc21 !important;
  box-sizing: border-box;
}
.dd_map_arcar_list_select p {
  font-weight: bold !important;
  color: #ffbc21 !important;
}
