import renderMyMarker from "@/class/DomMarker"
import audioMixins from "./audio";
import renderImgMarker from "@/class/ImgMarker"
import renderChatDomMarker from "@/class/ChatDomMarker"
import { createWebSocket, sendWebSocket, closeWebSocket } from "@/utils/socket";
import renderMarker from "@/mixins/renderMarker"
const tenantId = localStorage.getItem("sysTenantId")
const chatMarkerImg = [
  {
    id: '37763',
    gif: require("@/assets/icon/fjj.png"),
    static: require("@/assets/icon/fjj2.png"),
    sound: 'aisjiuxu',
    singleAgentId: tenantId == '11048' ? '1871453504569081856' : '1896530468791123968'
  },
  {
    id: '37703',
    gif: require("@/assets/icon/apl.png"),
    static: require("@/assets/icon/apl2.png"),
    sound: 'xiaoyan',
    singleAgentId: tenantId == '11048' ? '1871453448755478528' : '1896530594444083200'
  },
  {
    id: tenantId == '11048' ? '41186' : '41944',
    gif: require("@/assets/icon/lvxin.png"),
    static: require("@/assets/icon/lvxin.png"),
    sound: 'aisxping',
    singleAgentId: tenantId == '11048' ? '1871453362377981952' : '1896530357386215424'
  }
]
export default {
  mixins: [audioMixins, renderMarker],
  data: () => {
    return {
      singleAgentId: '1896763531118772224',
      mockQuestions: {
        index: 0,
        list: [
          '飞极迦先以欢快的语气给我打个招呼，然后再介绍一下自己吧！',
          "介绍一下阿派朗乐园",
          "飞极迦的活动有哪些？",
          "阿派朗的活动有哪些？",
        ],
        stop: false
      },
      multipleChat: {
        index: 0,
        list: [],
        stop: false
      },
      currentChat: {
        singleAgentId: '1896763531118772224',
        chatMarker: null,
        msgId: '',
        question: '',
        answer: '',
        multiple: false,
        stop: true   // 是否停止对话  true:停止对话  false:继续对话,
      },
      preChat: {},
      imgMarkers: [],
    }
  },
  mounted() {
    const token = localStorage.getItem("token")
    // const url = `wss://test.dreeck.com:11443/ddagentv2/ws/info?access_token=${token.split(' ')[1]}`
    const url = `wss://www.dreamdeck.cn:10443/ddagentv2/ws/info?access_token=${token.split(' ')[1]}&Tenant-Id=1`
    createWebSocket({ url }, this.receiveMsg)
    this.$on('poiRender', (poiList, version) => {
      if (version == 'style1') {
        this.renderMarker();
      } else if (version == 'style2') {
        // 当前不存在的点位
        const newMarkers = poiList.filter(item => this.markerLayer.findIndex(i => i.data.id == item.id) == -1)
        // 存在的AI智能体点位
        const chatMarkers = this.markerLayer.filter(item => this.imgMarkers.findIndex(i => i.data.id == item.data.id) > -1)
        // 存在的普通点位
        const markers = this.markerLayer.filter(item => this.imgMarkers.findIndex(i => i.data.id == item.data.id) == -1)
        const markerLayer = renderMyMarker(newMarkers, this.TXMap, this.tMap)
        this.markerLayer = markerLayer.concat(chatMarkers).concat(markers)
        this.markerLayer.map(item => {
          item.on('click', (e) => {
            console.log(e)
            this.markerClick({ geometry: { id: e.data.id } })
          });
        })
      }
    })
  },
  beforeDestroy() {
    closeWebSocket()
    this.clearChatMarkers()
    this.currentChat = {
      singleAgentId: '1896763531118772224',
      chatMarker: null,
      msgId: '',
      question: '',
      answer: '',
      multiple: false,
      stop: true   // 是否停止对话  true:停止对话  false:继续对话,
    }
  },
  methods: {
    handlePageHide() {
      // 页面隐藏时的处理逻辑
      // 停止所有音频播放
      if (this.source) {
        this.source.stop()
        this.source = null
      }
      if (this.audioData) {
        this.audioData.pause()
        this.audioData.currentTime = 0
        this.audioData = null
      }

      // 停止所有对话
      this.sendMsg('stop')
      this.mockQuestions.stop = true
      this.multipleChat.stop = true
      this.currentChat.stop = true
      this.currentChat.answer = ''

      // 关闭 WebSocket 连接
      closeWebSocket()

      // 清理聊天标记
      this.clearChatMarkers()
    },
    // 用户向智能体发送消息
    chatSend(e) {
      this.currentChat.singleAgentId = this.singleAgentId
      this.clearChatMarkers()
      console.log(e);
      this.mockQuestions.stop = true
      this.currentChat.stop = true
      this.currentChat.question = e
      this.currentChat.answer = ''
      if (this.source) this.source.stop();
      this.sendMsg('stop')
      this.sendMsg(e)
      this.chatMarkerList = []
      this.clearChatMarkers()
    },
    // 模拟对话
    dialogue() {
      if (this.mockQuestions.stop) return;
      this.sendMsg(this.mockQuestions.list[this.mockQuestions.index])
      this.currentChat.question = this.mockQuestions.list[this.mockQuestions.index]
      this.mockQuestions.index++
    },
    // 数据接收
    async receiveMsg(res) {
      let data = JSON.parse(res)
      if (data.type == 'chat') {
        this.stopType = true
        this.answerMsgData = JSON.parse(res)
        if (data.text.msg.indexOf('dreamdeck') > -1) {
          console.log('开始对话', data.text.msg);
          this.currentChat.msgId = data.text.msgId
        } else {
          if (this.currentChat.singleAgentId == this.singleAgentId) {
            if (data.text.msg == '<Finish>') {
              if (this.currentChat.answer.includes(',')) { // 多个智能体回答问题
                this.currentChat.multiple = true
                this.multipleChat.list = this.currentChat.answer.split(',').map(i => {
                  return {
                    singleAgentId: i,
                    chatMarker: null,
                    msgId: '',
                    question: this.currentChat.question,
                    answer: '',
                    stop: true   // 是否停止对话  true:停止对话  false:继续对话,
                  }
                })
                this.currentChat.singleAgentId = this.multipleChat.list[this.multipleChat.index].singleAgentId
                this.sendMsg(this.multipleChat.list[this.multipleChat.index].question)
                this.currentChat.answer = ''
              } else {
                if (this.currentChat.multiple) {
                  this.currentChat.singleAgentId = this.multipleChat.list[this.multipleChat.index].singleAgentId
                  this.sendMsg(this.multipleChat.list[this.multipleChat.index].question)
                } else {
                  this.currentChat.singleAgentId = this.currentChat.answer
                  this.sendMsg(this.currentChat.question)
                }
                this.currentChat.answer = ''
              }
            } else {
              this.currentChat.answer = this.currentChat.answer + data.text.msg
              // this.renderChatMarkerMsg()
              // if (this.currentChat.stop) {
              //   this.generateChatMarker()
              //   this.currentChat.stop = false
              // } else {
              //   this.renderChatMarkerMsg()
              // }
            }
          } else {
            if (data.text.msg == '<Finish>') {
              if (!this.mockQuestions.stop) {
                if (this.mockQuestions.index == 1) {
                  this.currentChat.answer = this.currentChat.answer + '阿派朗别摸鱼了，你也介绍一下自己吧！'
                }
                if (this.mockQuestions.index == 2) {
                  this.currentChat.answer = this.currentChat.answer + '飞极迦，你都准备了什么好玩的活动呀？'
                }
                if (this.mockQuestions.index == 3) {
                  this.currentChat.answer = this.currentChat.answer + '阿派朗，你有什么好玩的活动哇？'
                }
              }
              this.renderChatMarkerMsg()
              const marker = chatMarkerImg.find(item => item.singleAgentId == this.currentChat.singleAgentId)
              await this.speak(this.currentChat.answer, data.text.msgId, marker.sound)
              if (this.currentChat.multiple) {
                this.multipleChat.index++;
                this.currentChat.singleAgentId = this.multipleChat.index < this.multipleChat.list.length ? this.multipleChat.list[this.multipleChat.index].singleAgentId : ''
              } else {
                // if (this.mockQuestions.index == 1) {
                //   this.currentChat.singleAgentId = chatMarkerImg[1].singleAgentId
                // }
                // if (this.mockQuestions.index == 2) {
                //   this.currentChat.singleAgentId = chatMarkerImg[0].singleAgentId
                // }
                // if (this.mockQuestions.index == 3) {
                //   this.currentChat.singleAgentId = chatMarkerImg[1].singleAgentId
                // }
                this.currentChat.singleAgentId = this.singleAgentId
              }
              this.currentChat.stop = true
              this.currentChat.answer = ''
            } else {
              this.currentChat.answer = this.currentChat.answer + data.text.msg
              if (this.currentChat.stop) {
                this.generateChatMarker()
                this.currentChat.stop = false
              } else {
                this.renderChatMarkerMsg()
              }
            }
          }
        }
      }
    },
    // 生成对话框点位
    generateChatMarker() {
      // 先删除对应的普通点位
      const marker = chatMarkerImg.find(item => item.singleAgentId == this.currentChat.singleAgentId)
      const removeMarker = this.markerLayer.find(item => item.data.id == marker.id)
      removeMarker.setMap(null)
      // 再创建对应的图片点位和chat点位
      const addMarkerObj = this.markerList.find(item => item.id == marker.id)
      addMarkerObj.img = marker.gif
      addMarkerObj.img2 = marker.static
      addMarkerObj.singleAgentId = marker.singleAgentId
      const imgMarker = renderImgMarker(addMarkerObj, this.TXMap, this.tMap)
      imgMarker.on("click", (e) => {
        this.$wx.miniProgram.navigateTo({
          url: `/pages_agent/pages/chat/index?singleAgentId=${e.data.singleAgentId}&id=${e.data.id}&sound=${marker.sound}`,
          success: (res) => {
            this.handlePageHide()
          },
          fail: (res) => console.log(res)
        });
      });
      const index = this.imgMarkers.findIndex(i => i.data.id == imgMarker.data.id)
      if (index > -1) this.imgMarkers[index].setMap(null), this.imgMarkers[index] = imgMarker;
      else this.imgMarkers.push(imgMarker)
      // 添加对话框点位,此处需要做偏移
      const chatDomMarker = renderChatDomMarker(addMarkerObj, this.currentChat.msgId, this.TXMap, this.tMap)
      this.currentChat.chatMarker = { imgMarker, chatDomMarker }
    },
    renderChatMarkerMsg() {
      const dom = this.currentChat.chatMarker.chatDomMarker.dom.children[1]
      dom.innerHTML = this.currentChat.answer
    },
    updateImgMarker() {
      const imgMarker = this.currentChat.chatMarker.imgMarker
      console.log(imgMarker);
      imgMarker.dom.src = imgMarker.data.staticImg
    },
    // 发送数据
    sendMsg(msg) {
      if (msg == 'stop') {
        sendWebSocket({
          type: 'chat',
          msg: {
            msgStop: true,
            cache: false,
            description: '<Finish>',
            logId: this.currentChat.msgId,
            singleAgentId: this.currentChat.singleAgentId,
          }
        })
        return
      }
      sendWebSocket({
        type: 'chat',
        msg: {
          description: msg,
          cache: false,
          singleAgentId: this.currentChat.singleAgentId,
          msgStop: false
        }
      })
    },
    clearChatMarkers() {
      if (this.currentChat.chatMarker) {
        this.currentChat.chatMarker.chatDomMarker.setMap(null)
        this.imgMarkers.map(item => {
          item.setMap(null)
          item.map = null
          const marker = this.markerLayer.findIndex(i => i.data.id == item.data.id)
          console.log(this.markerLayer[marker]);
          this.markerLayer[marker].setMap(this.tMap)
        })
        this.imgMarkers = []
      }
    },
    markerClick(e) {
      console.log(e, '11111111111111');
      this.markerClickType = true;
      setTimeout(() => { this.markerClickType = false }, 500);
      let clickMarker = this.markerList.find((item) => item.id == e.geometry.id);
      if (clickMarker) {
        // 判断是否有选中  是 则清除上一个选中状态
        if (this.selectMarkerId) {
          let markerData = {
            id: this.selectMarkerId,
            styleId: `marker${this.particularsData.id}`,
            position: new this.TXMap.LatLng(
              Number(this.particularsData.latitude) * 1,
              Number(this.particularsData.longitude) * 1
            ),
            content: `${clickMarker.name}`,
          };
          if (!Array.isArray(this.markerLayer)) this.markerLayer.updateGeometries(markerData);
        }
        if (this.audioData) {
          this.audioType = false;
          this.audioData.pause();
        }
        // 修改为选中状态
        let data = {
          id: clickMarker.id,
          styleId: `marker${clickMarker.id}_select`,
          position: new this.TXMap.LatLng(
            Number(clickMarker.latitude) * 1,
            Number(clickMarker.longitude) * 1
          ),
          content: `${clickMarker.name}`,
        };
        if (!Array.isArray(this.markerLayer)) this.markerLayer.updateGeometries(data);
        this.selectMarkerId = clickMarker.id;
        this.particularsData = clickMarker;
        // console.log(this.particularsData);
        this.particularsData.coverUrl = `${process.env.VUE_APP_BASE_API}/admin/file/image/${clickMarker.cover}`;
        if (this.particularsData.arImg) {
          this.particularsData.arImgSrc = `${process.env.VUE_APP_BASE_API}/admin/file/image/${this.particularsData.arImg}`;
        }
        if (this.particularsData.music) {
          // this.audioData = new Audio(`${process.env.VUE_APP_BASE_API}/admin/file/${list[i].music}`)
          this.audioData = new Audio(
            `${process.env.VUE_APP_BASE_API}/${this.particularsData.music}`
          );
          this.audioData.loop = false;

          this.audioData.addEventListener("ended", () => {
            // console.log("结束");
            this.audioType = false;
          });
        }
        this.dotType = true;
        this.show = false; /* 底部详情弹窗出现，导航关闭 */
      }
    }
  }
}