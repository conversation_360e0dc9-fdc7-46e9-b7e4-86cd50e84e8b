


// import renderMyMarker from "@/class/DomMarker.js"
export default {
  data: () => {
    return {

    }
  },
  mounted() {
    //监听initMap函数执行完成
    this.$on('poiRender', (poiList) => {
      console.log(poiList, 'poiList');
      // this.markerLayer.map(item => item.destroy())
      // this.markerLayer = []
      // const markerLayer = renderMyMarker(poiList, this.TXMap, this.tMap)
    })
  },
  beforeDestroy() {

  },
  methods: {

  }
}