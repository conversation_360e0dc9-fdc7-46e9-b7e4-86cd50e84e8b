import renderMyMarker from "@/class/DomMarker"
import audioMixins from "./audio";
import renderImgMarker from "@/class/ImgMarker"
import renderChatDomMarker from "@/class/ChatDomMarker"
// import { createWebSocket, sendWebSocket, closeWebSocket } from "@/utils/socket";
import renderMarker from "@/mixins/renderMarker"
import { audioManager } from "@/utils/audioManager";
import { chatService, asrChatService } from "@/utils/asrChatService";
import { generateUUID } from '@/utils/utils';

const tenantId = localStorage.getItem("sysTenantId")
const chatMarkerImg = [
  {
    id: '37763',
    gif: require("@/assets/icon/fjj.png"),
    static: require("@/assets/icon/fjj2.png"),
    sound: 'aisjiuxu',
    voice: 'zh-CN-XiaoxiaoNeural',
    singleAgentId: tenantId == '11048' ? '1871453504569081856' : '1896530468791123968'
  },
  {
    id: '37703',
    gif: require("@/assets/icon/apl.png"),
    static: require("@/assets/icon/apl2.png"),
    sound: 'xiaoyan',
    voice: 'zh-CN-XiaoyiNeural',
    singleAgentId: tenantId == '11048' ? '1871453448755478528' : '1896530594444083200'
  },
  {
    id: tenantId == '11048' ? '41186' : '41944',
    gif: require("@/assets/icon/lvxin.png"),
    static: require("@/assets/icon/lvxin.png"),
    sound: 'aisxping',
    voice: 'zh-CN-YunjianNeural',
    singleAgentId: tenantId == '11048' ? '1871453362377981952' : '1896530357386215424'
  }
]
export default {
  mixins: [audioMixins, renderMarker],
  data: () => {
    return {
      socket: null, // 指向chatService的引用
      isAsrConnected: false,
      singleAgentId: '1896530468791123968', // 1896763531118772224
      mockQuestions: {
        index: 0,
        list: [
          '飞极迦先以欢快的语气给我打个招呼，然后再介绍一下自己吧！',
          "介绍一下阿派朗乐园",
          "飞极迦的活动有哪些？",
          "阿派朗的活动有哪些？",
        ],
        stop: false
      },
      multipleChat: {
        index: 0,
        list: [],
        stop: false
      },
      currentChat: {
        singleAgentId: '1896530468791123968',
        chatMarker: null,
        msgId: '',
        question: '',
        answer: '',
        multiple: false,
        stop: true,   // 是否停止对话  true:停止对话  false:继续对话
        singleAgentIds: [], // 当前对话的智能体ID列表
        index: 0, // 当前对话的智能体索引
        isFinish: true // 是否完成对话
      },
      // 新增：存储多个智能体的浮框信息
      chatMarkers: [], // 格式: [{agentId: 'xxx', marker: {imgMarker, chatDomMarker}, msgId: 'xxx', answer: 'xxx'}]
      // 消息列表
      logList: [],
      preChat: {},
      imgMarkers: [],
      // 常量配置
      chatConfig: {
        user_id: '',
        conversation_id: '',
        voice: 'zf_xiaobei',
        mode: 'KnowledgeBase',
        deviceType: 'sci_fitness'
      },
      userChatMsg: '',
    }
  },
  mounted() {
    // 确保audioManager已初始化并尝试解锁音频播放
    if (window.audioManager) {
      console.log("确保音频管理器已初始化");
      window.audioManager.tryUnlockAudio();
    }



    // 只保留新WebSocket初始化逻辑
    this.chatConfig.user_id = generateUUID();
    this.chatConfig.conversation_id = generateUUID();
    this.$on('poiRender', (poiList, version) => {
      if (version == 'style1') {
        this.renderMarker();
      } else if (version == 'style2') {
        // 当前不存在的点位
        const newMarkers = poiList.filter(item => this.markerLayer.findIndex(i => i.data.id == item.id) == -1)
        // 存在的AI智能体点位
        const chatMarkers = this.markerLayer.filter(item => this.imgMarkers.findIndex(i => i.data.id == item.data.id) > -1)
        // 存在的普通点位
        const markers = this.markerLayer.filter(item => this.imgMarkers.findIndex(i => i.data.id == item.data.id) == -1)
        const markerLayer = renderMyMarker(newMarkers, this.TXMap, this.tMap)
        this.markerLayer = markerLayer.concat(chatMarkers).concat(markers)
        this.markerLayer.map(item => {
          item.on('click', (e) => {
            console.log(e)
            this.markerClick({ geometry: { id: e.data.id } })
          });
        })
      }
    })
    this.initChat();
  },
  beforeDestroy() {
    this.clearChatMarkers()
    this.currentChat = {
      singleAgentId: '1896763531118772224',
      chatMarker: null,
      msgId: '',
      question: '',
      answer: '',
      multiple: false,
      stop: true,   // 是否停止对话  true:停止对话  false:继续对话
      singleAgentIds: [], // 当前对话的智能体ID列表
      index: 0, // 当前对话的智能体索引
      isFinish: true // 是否完成对话
    }

    // 关闭聊天服务
    this.cleanup();
  },
  methods: {


    // 初始化聊天服务和相关配置
    async initChat() {
      try {
        await this.initWebSocket();
      } catch (error) {
        console.error("初始化聊天失败:", error);
      }
    },
    handlePageHide() {
      // 页面隐藏时的处理逻辑
      // 停止所有音频播放
      if (this.source) {
        this.source.stop()
        this.source = null
      }
      if (this.audioData) {
        this.audioData.pause()
        this.audioData.currentTime = 0
        this.audioData = null
      }

      // 停止所有对话
      this.sendStop();
      this.mockQuestions.stop = true
      this.multipleChat.stop = true
      this.currentChat.stop = true
      this.currentChat.answer = ''

      // 清理聊天标记
      this.clearChatMarkers()
    },
    // 用户向智能体发送消息
    chatSend(e) {
      this.currentChat.singleAgentId = this.singleAgentId
      this.clearChatMarkers()
      console.log(e);
      this.mockQuestions.stop = true
      this.currentChat.stop = true
      this.currentChat.question = e
      this.currentChat.answer = ''
      if (this.source) this.source.stop();
      this.sendStop();
      this.sendMessage(e);
      this.chatMarkerList = []
      this.clearChatMarkers()

      // 不再设置userChatMsg，避免消息重复显示
      // this.userChatMsg = e;
    },
    /**
     * 发送聊天消息
     * @param {String} content - 消息内容
     */
    async sendMessage(content) {
      try {
        console.log('[sendMessage] called with content:', content);
        // 验证输入
        if (!content.trim()) {
          console.log('[sendMessage] content is empty, return');
          return;
        }

        // 停止之前的对话
        this.sendStop();

        // 清理之前的聊天标记
        this.clearChatMarkers();

        // 重置对话索引
        this.currentChat.singleAgentIds = [this.singleAgentId];
        this.currentChat.index = 0;
        this.currentChat.question = content;
        this.currentChat.msg = '';
        this.currentChat.stop = true; // 重置停止状态，以便生成新的聊天标记
        this.currentChat.answer = ''; // 清空之前的回答

        // 处理@的智能体 (chatMarkerImg中的智能体)
        if (content.includes('@')) {
          const matchedAgents = [];
          chatMarkerImg.forEach(item => {
            // 提取名字部分 (去掉.png/.jpg等后缀)
            const nameMatch = item.static.match(/([^\/]+)(?=\.png|\.jpg|$)/);
            const name = nameMatch ? nameMatch[0] : '';
            if (name && content.includes(`@${name}`)) {
              matchedAgents.push(item.singleAgentId);
            }
          });
          // 如果有@特定智能体，添加到当前聊天对象
          if (matchedAgents.length > 0) {
            this.currentChat.singleAgentIds = matchedAgents;
              }
            } else {
          // 没有@，则让所有chatMarkerImg里的智能体都回答
          this.currentChat.singleAgentIds = chatMarkerImg.map(item => item.singleAgentId);
        }

        // 添加用户消息到消息列表
        this.logList.push({
          id: Date.now().toString(),
          msg: content,
          timestamp: new Date().getTime(),
          type: 0, // 0表示用户消息
          username: "用户123",
          avatar: localStorage.getItem('avatar'),
        });

        // 获取当前智能体
        const currentAgentId = this.currentChat.singleAgentIds[this.currentChat.index];
        const currentAgent = chatMarkerImg.find(item => item.singleAgentId === currentAgentId);

        // 使用当前智能体的voice属性，如果找不到则使用默认voice
        const voice = currentAgent ? currentAgent.voice : this.chatConfig.voice;

        console.log('[sendMessage] about to call this.socket.send with:', {
          role: "user",
          content: content,
          voice: voice, // 使用智能体特定的voice
          agent_id: currentAgentId,
          mode: this.chatConfig.mode,
          user_id: this.chatConfig.user_id,
          conversation_id: this.chatConfig.conversation_id
        });
        // 发送消息到当前索引的智能体
        const success = await this.socket.send({
          role: "user",
          content: content,
          voice: voice, // 使用智能体特定的voice
          agent_id: currentAgentId,
          mode: this.chatConfig.mode,
          user_id: this.chatConfig.user_id,
          conversation_id: this.chatConfig.conversation_id
        });

        if (success) {
          console.log("[sendMessage] 消息发送成功");
          } else {
          console.error("[sendMessage] 消息发送失败");
        }
      } catch (error) {
        console.error("[sendMessage] 发送消息失败:", error);
      }
    },

    /**
     * 直接发送消息（不经过预处理）
     * @param {String} content - 消息内容
     */
    async sendDirectMessage(content) {
      try {
        console.log('[sendDirectMessage] called with content:', content);
        // 如果没有@任何人，默认发送给默认智能体
        if (this.currentChat.singleAgentIds.length === 0) {
          this.currentChat.singleAgentIds.push(this.singleAgentId);
        }

        // 停止当前播放
        await this.stopCurrentPlayback();

        // 添加用户消息到消息列表
        this.logList.push({
          id: Date.now().toString(),
          msg: content,
          timestamp: new Date().getTime(),
          type: 0, // 0表示用户消息
          username: "用户123",
          avatar: localStorage.getItem('avatar'),
        });

        // 获取当前智能体
        const currentAgentId = this.currentChat.singleAgentIds[this.currentChat.index];
        const currentAgent = chatMarkerImg.find(item => item.singleAgentId === currentAgentId);

        // 使用当前智能体的voice属性，如果找不到则使用默认voice
        const voice = currentAgent ? currentAgent.voice : this.chatConfig.voice;

        console.log('[sendDirectMessage] about to call this.socket.send with:', {
          role: "user",
          content: content,
          voice: voice, // 使用智能体特定的voice
          agent_id: currentAgentId,
          mode: this.chatConfig.mode,
          user_id: this.chatConfig.user_id,
          conversation_id: this.chatConfig.conversation_id
        });
        // 发送消息到服务器
        const success = await this.socket.send({
          role: "user",
          content: content,
          voice: voice, // 使用智能体特定的voice
          agent_id: currentAgentId,
          mode: this.chatConfig.mode,
          user_id: this.chatConfig.user_id,
          conversation_id: this.chatConfig.conversation_id
        });

        if (success) {
          console.log("[sendDirectMessage] 消息发送成功");
        } else {
          console.error("[sendDirectMessage] 消息发送失败");
        }
      } catch (error) {
        console.error("[sendDirectMessage] 直接发送消息失败:", error);
      }
    },

    /**
     * 发送聊天消息到服务器
     */
    async sendChatMessage() {
      try {
        console.log('[sendChatMessage] called');
        // 停止当前播放
        await this.stopCurrentPlayback();

        // 获取当前智能体
        const currentAgentId = this.currentChat.singleAgentIds[this.currentChat.index];
        const currentAgent = chatMarkerImg.find(item => item.singleAgentId === currentAgentId);

        // 使用当前智能体的voice属性，如果找不到则使用默认voice
        const voice = currentAgent ? currentAgent.voice : this.chatConfig.voice;

        console.log('[sendChatMessage] about to call this.socket.send with:', {
          role: "user",
          content: this.currentChat.question,
          agent_id: currentAgentId,
          voice: voice, // 使用智能体特定的voice
          mode: this.chatConfig.mode,
          user_id: this.chatConfig.user_id,
          conversation_id: this.chatConfig.conversation_id
        });

        // 使用新的消息格式发送消息
        const success = await this.socket.send({
          role: "user",
          content: this.currentChat.question,
          agent_id: currentAgentId,
          voice: voice, // 使用智能体特定的voice
          mode: this.chatConfig.mode,
          user_id: this.chatConfig.user_id,
          conversation_id: this.chatConfig.conversation_id
        });

        if (success) {
          console.log("[sendChatMessage] 消息发送成功");
            } else {
          console.error("[sendChatMessage] 消息发送失败");
        }
      } catch (error) {
        console.error("[sendChatMessage] 发送聊天消息失败:", error);
      }
    },

    /**
     * 处理语音输入
     * @param {string} content - 语音识别的文本内容
     */
    async handleVoiceInput(content) {
      try {
        if (!content) {
          console.log("语音识别内容为空，不处理");
          return;
        }

        console.log("处理语音输入:", content);

        // 停止当前播放
        await this.stopCurrentPlayback();

        // 发送消息
        this.sendMessage(content);
      } catch (error) {
        console.error("处理语音输入失败:", error);
      }
    },

    /**
     * 停止当前音频播放
     */
    async stopCurrentPlayback() {
      try {
        // 重置所有语音播放状态
        this.resetAllVoicePlay();

        // 停止音频管理器
        await audioManager.stop();
      } catch (error) {
        console.error("停止当前播放失败:", error);
      }
    },

    /**
     * 模拟对话
     */
    dialogue() {
      if (this.mockQuestions.stop) return;
      this.sendMessage(this.mockQuestions.list[this.mockQuestions.index]);
      this.currentChat.question = this.mockQuestions.list[this.mockQuestions.index];
      this.mockQuestions.index++;
    },

    // 为特定智能体生成对话框点位
    generateAgentChatMarker(agentId, msgId, initialAnswer = '') {
      // 查找智能体信息
      const marker = chatMarkerImg.find(item => item.singleAgentId == agentId);
      if (!marker) {
        console.error('找不到对应的智能体:', agentId);
        return null;
      }

      // 先删除对应的普通点位
      const removeMarker = this.markerLayer.find(item => item.data.id == marker.id);
      if (removeMarker) {
        removeMarker.setMap(null);
      }

      // 再创建对应的图片点位和chat点位
      const addMarkerObj = this.markerList.find(item => item.id == marker.id);
      if (!addMarkerObj) {
        console.error('找不到对应的标记对象:', marker.id);
        return null;
      }

      // 设置图片属性
      addMarkerObj.img = marker.gif;
      addMarkerObj.img2 = marker.static;
      addMarkerObj.singleAgentId = marker.singleAgentId;

      // 创建图片标记
      const imgMarker = renderImgMarker(addMarkerObj, this.TXMap, this.tMap);
      imgMarker.on("click", (e) => {
        this.$wx.miniProgram.navigateTo({
          url: `/pages_agent/pages/chat/index?singleAgentId=${e.data.singleAgentId}&id=${e.data.id}&sound=${marker.sound}`,
          success: (res) => {
            this.handlePageHide();
          },
          fail: (res) => console.log(res)
        });
      });

      // 管理图片标记
      const index = this.imgMarkers.findIndex(i => i.data.id == imgMarker.data.id);
      if (index > -1) {
        this.imgMarkers[index].setMap(null);
        this.imgMarkers[index] = imgMarker;
      } else {
        this.imgMarkers.push(imgMarker);
      }

      // 添加对话框点位
      const chatDomMarker = renderChatDomMarker(addMarkerObj, msgId, this.TXMap, this.tMap);

      // 如果有初始回答内容，设置内容
      if (initialAnswer) {
        chatDomMarker.dom.children[1].innerHTML = initialAnswer;
      }

      // 创建标记信息对象
      const markerInfo = {
        agentId,
        msgId,
        answer: initialAnswer,
        marker: { imgMarker, chatDomMarker }
      };

      // 添加到标记数组
      this.chatMarkers.push(markerInfo);

      return markerInfo;
    },
    // 生成对话框点位（兼容旧版实现）
    generateChatMarker() {
      const agentId = this.currentChat.singleAgentId;
      const msgId = this.currentChat.msgId || Date.now().toString();
      const answer = this.currentChat.answer || '';

      // 使用新方法创建标记
      const markerInfo = this.generateAgentChatMarker(agentId, msgId, answer);

      // 更新currentChat引用
      if (markerInfo) {
        this.currentChat.chatMarker = markerInfo.marker;
      }
    },

    // 渲染聊天标记消息（兼容旧版实现）
    renderChatMarkerMsg() {
      if (this.currentChat.chatMarker && this.currentChat.chatMarker.chatDomMarker) {
        const dom = this.currentChat.chatMarker.chatDomMarker.dom.children[1];
        dom.innerHTML = this.currentChat.answer;
      } else {
        // 尝试使用新方法更新
        this.updateAgentChatMarker(this.currentChat.singleAgentId, this.currentChat.answer);
      }
    },

    // 更新图片标记（兼容旧版实现）
    updateImgMarker() {
      if (this.currentChat.chatMarker && this.currentChat.chatMarker.imgMarker) {
        const imgMarker = this.currentChat.chatMarker.imgMarker;
        imgMarker.dom.src = imgMarker.data.staticImg;
      } else {
        // 尝试使用新方法更新
        this.updateAgentImageMarker(this.currentChat.singleAgentId);
      }
    },

    clearChatMarkers() {
      // 清理所有聊天浮框
      for (let markerInfo of this.chatMarkers) {
        if (markerInfo.marker && markerInfo.marker.chatDomMarker) {
          markerInfo.marker.chatDomMarker.setMap(null);
        }
      }

      // 清理图片标记并恢复原始标记
      if (this.imgMarkers.length > 0) {
        this.imgMarkers.forEach(item => {
          item.setMap(null);
          item.map = null;
          const marker = this.markerLayer.findIndex(i => i.data.id == item.data.id);
          if (marker !== -1) {
            this.markerLayer[marker].setMap(this.tMap);
          }
        });
        this.imgMarkers = [];
      }

      // 重置数组
      this.chatMarkers = [];

      // 兼容旧版处理
      if (this.currentChat.chatMarker) {
        this.currentChat.chatMarker.chatDomMarker.setMap(null);
        this.currentChat.chatMarker = null;
      }

      // 清理当前聊天状态中的累计消息
      this.currentChat.answer = '';
      this.currentChat.msg = '';
    },
    markerClick(e) {
      console.log(e, '11111111111111');
      this.markerClickType = true;
      setTimeout(() => { this.markerClickType = false }, 500);
      let clickMarker = this.markerList.find((item) => item.id == e.geometry.id);
      if (clickMarker) {
        // 判断是否有选中  是 则清除上一个选中状态
        if (this.selectMarkerId) {
          let markerData = {
            id: this.selectMarkerId,
            styleId: `marker${this.particularsData.id}`,
            position: new this.TXMap.LatLng(
              Number(this.particularsData.latitude) * 1,
              Number(this.particularsData.longitude) * 1
            ),
            content: `${clickMarker.name}`,
          };
          if (!Array.isArray(this.markerLayer)) this.markerLayer.updateGeometries(markerData);
        }
        if (this.audioData) {
          this.audioType = false;
          this.audioData.pause();
        }
        // 修改为选中状态
        let data = {
          id: clickMarker.id,
          styleId: `marker${clickMarker.id}_select`,
          position: new this.TXMap.LatLng(
            Number(clickMarker.latitude) * 1,
            Number(clickMarker.longitude) * 1
          ),
          content: `${clickMarker.name}`,
        };
        if (!Array.isArray(this.markerLayer)) this.markerLayer.updateGeometries(data);
        this.selectMarkerId = clickMarker.id;
        this.particularsData = clickMarker;
        // console.log(this.particularsData);
        this.particularsData.coverUrl = `${process.env.VUE_APP_BASE_API}/admin/file/image/${clickMarker.cover}`;
        if (this.particularsData.arImg) {
          this.particularsData.arImgSrc = `${process.env.VUE_APP_BASE_API}/admin/file/image/${this.particularsData.arImg}`;
        }
        if (this.particularsData.music) {
          // this.audioData = new Audio(`${process.env.VUE_APP_BASE_API}/admin/file/${list[i].music}`)
          this.audioData = new Audio(
            `${process.env.VUE_APP_BASE_API}/${this.particularsData.music}`
          );
          this.audioData.loop = false;

          this.audioData.addEventListener("ended", () => {
            // console.log("结束");
            this.audioType = false;
          });
        }
        this.dotType = true;
        this.show = false; /* 底部详情弹窗出现，导航关闭 */
      }
    },
    /**
     * 清理资源
     */
    cleanup() {
      console.log('清理资源', this.socket);

      // 关闭聊天服务
      if (this.socket) this.socket.close();

      // 关闭语音识别服务
      asrChatService.close();
      this.isAsrConnected = false;

      // 停止当前对话
      if (!this.currentChat.isFinish) this.sendStop();

      // 清理音频资源
      audioManager.stop();
    },

    /**
     * 初始化WebSocket连接
     */
    async initWebSocket() {
      try {
        const token = localStorage.getItem("token");
        const tenantId = localStorage.getItem("sysTenantId");

        // 构建WebSocket URL
        const wsUrl = `wss://agentservice8600.dreeck.com/audio/chat-tts?access_token=${token.split(' ')[1]}&Tenant-Id=${tenantId}`;

        // 构建头信息
        const headers = {
          Voice: this.chatConfig.voice,
          AuthToken: token.split(' ')[1],
          Mode: this.chatConfig.mode,
          DeviceType: this.chatConfig.deviceType,
        };

        // 初始化聊天服务
        const success = await chatService.init({
          wsUrl,
          headers,
          onMessage: this.handleMessage,
          onError: (error) => {
            console.error("聊天服务错误:", error);
          }
        });

        if (success) {
          console.log("聊天服务初始化成功");
          this.socket = chatService; // 保持兼容性
        } else {
          console.error("聊天服务初始化失败");
        }

        // 同时初始化语音识别 WebSocket
        await this.initAsrWebSocket();

      } catch (error) {
        console.error("WebSocket连接失败:", error);
      }
    },

    /**
     * 初始化语音识别 WebSocket 连接
     */
    async initAsrWebSocket() {
      try {
        console.log("初始化 ASR WebSocket 连接");

        // 获取token和tenantId
        const token = localStorage.getItem("token");
        const tenantId = localStorage.getItem("sysTenantId");

        // 构建WebSocket URL
        const wsUrl = `wss://agentservice8600.dreeck.com/audio/asr-chat-tts?access_token=${token.split(' ')[1]}&Tenant-Id=${tenantId}`;


        // 构建头信息
        const headers = {
          AuthToken: token.split(' ')[1],
          Voice: this.chatConfig.voice,
          Mode: this.chatConfig.mode,
          DeviceType: this.chatConfig.deviceType,
          AgentId: this.singleAgentId,
          UserId: this.chatConfig.user_id,
          ConversationId: this.chatConfig.conversation_id,
          SampleRate: "16000",
          StartValue: "4000",
          EndValue: "3000",
          ChunkSize: "1024",
          Role: "BotA"
        };

        // 初始化语音识别服务
        const success = await asrChatService.init({
          wsUrl,
          headers,
          asrMode: 'websocket',

          // 用户语音识别结果回调
          onUserSpeechRecognized: (content) => {
            console.log('用户语音识别结果:', content);
            this.handleVoiceInput(content);
          },

          // 机器人回答回调
          onBotResponse: (data) => {
            console.log('机器人回答:', data);
            // this.handleBotResponse(data);
          },

          // 对话结束回调
          onFinish: () => {
            console.log('对话结束');
            this.scrollToLower();
          },

          // 错误回调
          onError: (error) => {
            console.error('语音识别服务错误:', error);
          }
        });

        if (success) {
          console.log("语音识别服务初始化成功");
          this.isAsrConnected = true;
        } else {
          console.error("语音识别服务初始化失败");
          this.isAsrConnected = false;
        }
      } catch (error) {
        console.error("ASR WebSocket 连接失败:", error);
        this.isAsrConnected = false;
      }
    },

    /**
     * 处理WebSocket接收到的消息
     * @param {Object} msg - WebSocket消息对象
     */
    async handleMessage(msg) {
      try {
        // 检查msg和msg.data是否存在
        if (!msg || !msg.data) {
          console.error("收到无效的WebSocket消息:", msg);
          return;
        }

        // 尝试解析JSON数据
        let data;
        try {
          // 检查msg.data是否为字符串且不为空
          if (typeof msg.data === 'string' && msg.data.trim() !== '') {
            data = JSON.parse(msg.data);
          } else if (typeof msg.data === 'object') {
            // 如果已经是对象，直接使用
            data = msg.data;
          } else {
            console.error("无法解析的WebSocket消息数据类型:", typeof msg.data);
            return;
          }
        } catch (parseError) {
          console.error("解析WebSocket消息失败:", parseError, "原始数据:", msg.data);
          return;
        }

        // 检查解析后的数据是否有效
        if (!data) {
          console.error("解析后的WebSocket消息数据为空");
          return;
        }

        console.log("解析后的WebSocket消息数据:", data);

        // 如果是第一条消息且是用户消息，停止当前播放
        if (this.logList.length > 0 && this.logList[this.logList.length - 1].type === 0) {
          // 停止当前播放
          this.resetAllVoicePlay();
          await audioManager.stop();
        }

        // 处理消息
        switch (data.content) {
          case "<Finish>":
            // 处理完成消息
            const lastMessage = this.logList[this.logList.length - 1];
            this.currentChat.isFinish = true;

            // 获取当前智能体ID
            const currentAgentId = this.currentChat.singleAgentIds[this.currentChat.index];

            // 如果存在聊天标记，更新图标为静态图标
            this.updateAgentImageMarker(currentAgentId);

            // 兼容旧版处理
            if (this.currentChat.chatMarker && this.currentChat.chatMarker.imgMarker) {
              this.updateImgMarker();
            }

            audioManager
                .waitForPlaybackComplete()
                .then(() => {
                  console.log("所有音频播放完成，重置播放状态");
                  if (lastMessage) lastMessage.voicePlay = false;
                  // 如果有下一个智能体需要对话，继续发送消息
                  this.nextMessage();
                })
                .catch((err) => {
                  // 如果有下一个智能体需要对话，继续发送消息
                  if (lastMessage) lastMessage.voicePlay = false;
                  this.nextMessage();
                });
            break;

          default:
            // 处理常规消息
            await this.handleBotResponse(data);
            break;
        }
      } catch (error) {
        console.error("处理消息失败:", error);
      }
    },

    /**
     * 处理机器人回答（从ASR WebSocket接收到的带有wav_stream的消息）
     * @param {Object} data - 包含content和wav_stream的数据对象
     */
    async handleBotResponse(data) {
      try {
        // 标记对话未完成
        this.currentChat.isFinish = false;

        // 获取当前应该回复的智能体
        const currentAgentId = this.currentChat.singleAgentIds[this.currentChat.index];
        const currentAgent = chatMarkerImg.find(item =>
          item.singleAgentId === currentAgentId);

        if (!currentAgent) {
          console.error("找不到对应的智能体:", currentAgentId);
          return;
        }

        // 检查最后一条消息是否是当前智能体的回复
        const lastMessage = this.logList.length > 0 ? this.logList[this.logList.length - 1] : null;
        const needNewMessage = !lastMessage ||
                              lastMessage.type === 0 || // 上一条是用户消息
                              (lastMessage.type === 1 && lastMessage.username !== currentAgent.name); // 上一条是其他智能体的消息

        // 如果需要创建新消息
        if (needNewMessage) {
          console.log("添加机器人回复消息:", currentAgent.name);
          this.logList.push({
            id: Date.now().toString(),
            msg: '',
            timestamp: new Date().getTime(),
            type: 1, // 1表示机器人消息
            username: currentAgent.name,
            avatar: currentAgent.gif,
            voicePlay: true,
            voiceTime: 0,
            audio_urls: []
          });
        }

        // 更新消息内容
        if (data.content) {
          // 获取最后一条消息
          const lastMessage = this.logList[this.logList.length - 1];

          // 确保最后一条消息是当前智能体的消息
          if (lastMessage && lastMessage.type === 1 && lastMessage.username === currentAgent.name) {
            // 累加消息内容
            lastMessage.msg = (lastMessage.msg || '') + data.content;

            // 清除地图上所有现有的聊天标记，确保一次只显示一个
            this.clearChatMarkers();

            // 为当前智能体创建新的标记
            const msgId = Date.now().toString();
            const markerInfo = this.generateAgentChatMarker(currentAgentId, msgId, lastMessage.msg);

            // 兼容旧版属性
            if (this.currentChat.index === 0) {
              this.currentChat.answer = lastMessage.msg;
              this.currentChat.msgId = markerInfo ? markerInfo.msgId : msgId;
              this.currentChat.chatMarker = markerInfo ? markerInfo.marker : null;
              this.currentChat.stop = false;
            }
          } else {
            console.error("无法更新消息内容：最后一条消息不是当前智能体的消息");
          }
        }

        // 处理音频
        if (data.wav_stream) {
          try {
            // 获取最后一条消息
            const lastMessage = this.logList[this.logList.length - 1];

            // 确保最后一条消息是当前智能体的消息
            if (lastMessage && lastMessage.type === 1 && lastMessage.username === currentAgent.name) {
              // 处理音频流
              const audioPath = await audioManager.handleWavStream(data.wav_stream);
              console.log("音频处理完成:", audioPath);

              // 添加到消息的音频列表
              if (!lastMessage.audio_urls) {
                lastMessage.audio_urls = [];
              }
              lastMessage.audio_urls.push(audioPath);

              // 更新音频时长
              try {
                const duration = await this.getAudioDuration(audioPath);
                lastMessage.voiceTime = (lastMessage.voiceTime || 0) + duration;
                console.log("更新音频时长:", lastMessage.voiceTime);
              } catch (durationError) {
                console.error("获取音频时长失败:", durationError);
              }

              // 播放音频
              try {
                await audioManager.play(audioPath);
              } catch (error) {
                console.warn('音频播放失败:', error);
                // 如果自动播放失败，设置为手动播放
                lastMessage.voicePlay = false;
              }
            } else {
              console.error("无法处理音频：最后一条消息不是当前智能体的消息");
            }
          } catch (error) {
            console.error("处理音频失败:", error);
          }
        }
      } catch (error) {
        console.error("处理机器人回答失败:", error);
      }
    },

    /**
     * 重置所有音频播放状态
     */
    resetAllVoicePlay() {
      if (this.logList && this.logList.length > 0) {
        this.logList.forEach(item => {
          if (item.voicePlay) {
            item.voicePlay = false;
          }
        });
      }
    },

    /**
     * 进入下一条消息处理
     */
    nextMessage() {
      if (this.currentChat.singleAgentIds.length > 0) {
        if (this.currentChat.index < this.currentChat.singleAgentIds.length - 1) {
          // 清除前一个智能体的聊天标记
          this.clearChatMarkers();

          // 清理前一个智能体的消息数据，确保不会累加到下一个智能体
          this.clearPreviousAgentMessage();

          this.currentChat.index += 1;
          this.currentChat.msg = '';
          this.sendChatMessage();
        } else {
          this.resetCurrentChat();
        }
      } else {
        this.resetCurrentChat();
      }
    },

    /**
     * 清理前一个智能体的消息数据
     */
    clearPreviousAgentMessage() {
      // 获取前一个智能体ID
      const previousIndex = this.currentChat.index;
      if (previousIndex >= 0 && previousIndex < this.currentChat.singleAgentIds.length) {
        const previousAgentId = this.currentChat.singleAgentIds[previousIndex];
        const previousAgent = chatMarkerImg.find(item => item.singleAgentId === previousAgentId);

        if (previousAgent) {
          // 查找并清理前一个智能体的消息
          const messagesToRemove = [];
          for (let i = this.logList.length - 1; i >= 0; i--) {
            const message = this.logList[i];
            if (message.type === 1 && message.username === previousAgent.name) {
              messagesToRemove.push(i);
            } else if (message.type === 0) {
              // 遇到用户消息就停止，避免删除过多消息
              break;
            }
          }

          // 从后往前删除消息，避免索引错乱
          messagesToRemove.forEach(index => {
            this.logList.splice(index, 1);
          });

          console.log(`清理了前一个智能体 ${previousAgent.name} 的 ${messagesToRemove.length} 条消息`);
        }
      }
    },

    /**
     * 重置当前聊天状态
     */
    resetCurrentChat() {
      // 清理聊天标记
      this.clearChatMarkers();

      // 重置聊天状态
      this.currentChat.index = 0;
      this.currentChat.msg = '';
      this.currentChat.answer = '';
      this.currentChat.stop = true;
      this.currentChat.singleAgentIds = [this.singleAgentId];
    },

    /**
     * 获取音频时长
     * @param {String} src - 音频文件路径
     * @returns {Promise<Number>} - 音频时长（秒）
     */
    getAudioDuration(src) {
      return audioManager.getAudioDuration(src);
    },

    /**
     * 尝试滚动到底部 (为了兼容性保留)
     */
    scrollToLower() {
      try {
        // 这里可以添加滚动逻辑，如果需要
      } catch (error) {
        console.error('滚动失败:', error);
      }
    },

    /**
     * 播放或停止语音
     * @param {Number} index - 消息索引
     */
    async playVoice(index) {
      try {
        const currentItem = this.logList[index];
        console.log("点击播放按钮:", currentItem);

        // 判断如果没有音频则不播放
        if (
          !currentItem ||
          !currentItem.audio_urls ||
          currentItem.audio_urls.length === 0 ||
          currentItem.voiceTime <= 0
        ) {
          console.log("没有可播放的音频");
          return;
        }

        // 如果当前项目正在播放，则停止播放
        if (currentItem.voicePlay) {
          console.log("停止当前播放");
          audioManager.stop();
          currentItem.voicePlay = false;
          return;
        }

        // 停止所有正在播放的音频并重置状态
        this.resetAllVoicePlay();

        // 等待audioManager完全停止并重置状态
        console.log("等待audioManager完全停止并重置状态...");
        await audioManager.stop();

        // 设置当前项的播放状态
        currentItem.voicePlay = true;

        // 禁用超时处理，防止音频播放被超时中断
        audioManager.setTimeoutEnabled(false);

        try {
          // 依次播放所有音频
          for (
            let i = 0;
            i < currentItem.audio_urls.length && currentItem.voicePlay;
            i++
          ) {
            const audioPath = currentItem.audio_urls[i];
            console.log(
              `播放音频 ${i + 1}/${currentItem.audio_urls.length}:`,
              audioPath
            );

            // 直接播放音频，不使用队列
            await audioManager.playDirectly(audioPath, 1);

            // 如果播放过程中停止了，则中断循环
            if (!currentItem.voicePlay) {
              console.log("播放过程中停止了，中断循环");
              break;
            }
          }

          // 检查当前项是否仍在播放状态
          if (currentItem.voicePlay) {
            console.log("所有音频播放完成，重置状态");
            currentItem.voicePlay = false;
          }
        } catch (error) {
          console.error("音频播放过程中出错:", error);
          // 重置播放状态
          if (currentItem.voicePlay) {
            currentItem.voicePlay = false;
          }
        } finally {
          // 恢复超时处理的设置
          audioManager.setTimeoutEnabled(true);
        }
      } catch (error) {
        console.error("播放语音失败:", error);

        // 重置播放状态
        if (index >= 0 && this.logList[index]) {
          this.logList[index].voicePlay = false;
        }
      }
    },
    /**
     * 发送停止信号
     */
    sendStop() {
      if (!this.currentChat.isFinish && this.socket) {
        // 获取当前智能体
        const currentAgentId = this.currentChat.singleAgentIds[this.currentChat.index];
        const currentAgent = chatMarkerImg.find(item => item.singleAgentId === currentAgentId);

        // 使用当前智能体的voice属性，如果找不到则使用默认voice
        const voice = currentAgent ? currentAgent.voice : this.chatConfig.voice;

        // 使用新的消息格式发送停止信号
        this.socket.send({
          role: "user",
          content: "<Finish>",
          agent_id: currentAgentId,
          voice: voice, // 使用智能体特定的voice
          mode: this.chatConfig.mode,
          user_id: this.chatConfig.user_id,
          conversation_id: this.chatConfig.conversation_id
        });
        console.log("发送停止信号");
      }
    },
    // 更新特定智能体的对话框内容
    updateAgentChatMarker(agentId, content) {
      // 查找智能体的标记信息
      const markerInfo = this.chatMarkers.find(item => item.agentId === agentId);
      if (!markerInfo || !markerInfo.marker || !markerInfo.marker.chatDomMarker) {
        console.error('找不到智能体的标记信息:', agentId);
        return false;
      }

      // 更新内容
      const dom = markerInfo.marker.chatDomMarker.dom.children[1];
      if (dom) {
        dom.innerHTML = content;
        // 更新缓存的回答内容
        markerInfo.answer = content;
        return true;
      }

      return false;
    },

    // 将智能体的图标从动态图切换为静态图
    updateAgentImageMarker(agentId) {
      const markerInfo = this.chatMarkers.find(item => item.agentId === agentId);
      if (!markerInfo || !markerInfo.marker || !markerInfo.marker.imgMarker) {
        console.error('找不到智能体的标记信息:', agentId);
        return false;
      }

      const imgMarker = markerInfo.marker.imgMarker;
      imgMarker.dom.src = imgMarker.data.staticImg;
      return true;
    },
  }
}