

import renderImgMarker from "@/class/ImgMarker"
import renderChatDomMarker from "@/class/ChatDomMarker"
export default {
  data: () => {
    return {
      imgMarkers: [],
    }
  },
  mounted() {
  },
  beforeDestroy() {
  },
  methods: {
    // 渲染 marker
    renderMarker() {
      this.markerLayer = new this.TXMap.MultiMarker({
        id: "marker-layer",
        map: this.tMap,
        styles: this.handleStyleMarker(),
        geometries: this.handleMarker(),
      });
      this.markerLayer.on("click", this.markerClick);
    },
    // marker style 数据处理
    handleStyleMarker() {
      let myMarkers = {};
      this.markerList.map((item, index) => {
        myMarkers[`marker${item.id}`] = new TMap.MarkerStyle({
          width: 38, // 点标记样式宽度（像素）
          height: 43, // 点标记样式高度（像素）
          src: item.icon
            ? `${process.env.VUE_APP_BASE_API}/admin/file/image/${item.icon}`
            : require("../assets/typechecked.png"), //图片路径
          //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
          anchor: { x: 19, y: 21.5 },
          offset: { x: 0, y: 24 },
          color: "#676767",
          strokeWidth: 1,
          strokeColor: "#fff",
        });
        myMarkers[`marker${item.id}_select`] = new TMap.MarkerStyle({
          width: 64, // 点标记样式宽度（像素）
          height: 71, // 点标记样式高度（像素）
          // "src": 'https://www.dreamdeck.cn:10443/app/icons/southtwoloop/saas_markerselect.png',  //图片路径
          src: item.icon
            ? `${process.env.VUE_APP_BASE_API}/admin/file/image/${item.icon}`
            : require("../assets/typechecked.png"), //图片路径
          //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
          anchor: { x: 32, y: 35.5 },
          offset: { x: 0, y: 34 },
          color: "#000",
          strokeWidth: 2,
          strokeColor: "#fff",
        });
      });
      return myMarkers;
    },
    // 数据处理
    handleMarker() {
      let myMarkers = [];
      this.markerList.map((item, index) => {
        let obj = {
          id: item.id,
          styleId:
            item.selectMarkerId !== 0
              ? `marker${item.id}_select`
              : `marker${item.id}`,
          // styleId: item.selectMarkerId !== 0 ? "marker_select" : 'myStyle',
          position: new this.TXMap.LatLng(
            Number(item.latitude) * 1,
            Number(item.longitude) * 1
          ),
          content: `${item.name}`,
        };
        myMarkers.push(obj);
      });
      return myMarkers;
    },
    renderChatMarkers(chatObj) {
      if (chatObj.id == '1') chatObj.id = '41186'
      // 先删除对应的普通点位
      const removeMarker = this.markerLayer.find(item => item.data.id == chatObj.id)
      removeMarker.setMap(null)
      // 在添加对应的dom点位
      const addMarkerObj = this.markerList.find(item => item.id == chatObj.id)
      console.log(addMarkerObj, '=============================');
      addMarkerObj.img = chatObj.img
      addMarkerObj.img2 = chatObj.img2
      const imgMarker = renderImgMarker(addMarkerObj, this.TXMap, this.tMap)
      console.log(imgMarker, '=============================');
      imgMarker.on("click", (e) => {
        this.$wx.miniProgram.navigateTo({
          url: `/pages_agent/pages/chat/index?singleAgentId=${this.singleAgentId}&id=${imgMarker.data.id}`,
          success: (res) => {
            this.handlePageHide()
          },
        });
      });
      const index = this.imgMarkers.findIndex(i => i.data.id == imgMarker.data.id)
      if (index > -1) this.imgMarkers[index] = imgMarker;
      else this.imgMarkers.push(imgMarker)
      // 添加对话框点位,此处需要做偏移
      const chatDomMarker = renderChatDomMarker(addMarkerObj, chatObj.answer, chatObj.msgId, this.TXMap, this.tMap)
      this.currentChat.chatMarker = { imgMarker, chatDomMarker }
      this.speak(chatObj.answer, chatObj.msgId, chatObj.sound)
    },
    markerClick(e) {
      console.log(e, '11111111111111');
      this.markerClickType = true;
      setTimeout(() => { this.markerClickType = false }, 500);
      let clickMarker = this.markerList.find((item) => item.id == e.geometry.id);
      if (clickMarker) {
        // 判断是否有选中  是 则清除上一个选中状态
        if (this.selectMarkerId) {
          let markerData = {
            id: this.selectMarkerId,
            styleId: `marker${this.particularsData.id}`,
            position: new this.TXMap.LatLng(
              Number(this.particularsData.latitude) * 1,
              Number(this.particularsData.longitude) * 1
            ),
            content: `${clickMarker.name}`,
          };
          if (!Array.isArray(this.markerLayer)) this.markerLayer.updateGeometries(markerData);
        }
        if (this.audioData) {
          this.audioType = false;
          this.audioData.pause();
        }
        // 修改为选中状态
        let data = {
          id: clickMarker.id,
          styleId: `marker${clickMarker.id}_select`,
          position: new this.TXMap.LatLng(
            Number(clickMarker.latitude) * 1,
            Number(clickMarker.longitude) * 1
          ),
          content: `${clickMarker.name}`,
        };
        if (!Array.isArray(this.markerLayer)) this.markerLayer.updateGeometries(data);
        this.selectMarkerId = clickMarker.id;
        this.particularsData = clickMarker;
        // console.log(this.particularsData);
        this.particularsData.coverUrl = `${process.env.VUE_APP_BASE_API}/admin/file/image/${clickMarker.cover}`;
        if (this.particularsData.arImg) {
          this.particularsData.arImgSrc = `${process.env.VUE_APP_BASE_API}/admin/file/image/${this.particularsData.arImg}`;
        }
        if (this.particularsData.music) {
          // this.audioData = new Audio(`${process.env.VUE_APP_BASE_API}/admin/file/${list[i].music}`)
          this.audioData = new Audio(
            `${process.env.VUE_APP_BASE_API}/${this.particularsData.music}`
          );
          this.audioData.loop = false;

          this.audioData.addEventListener("ended", () => {
            // console.log("结束");
            this.audioType = false;
          });
        }
        this.dotType = true;
        this.show = false; /* 底部详情弹窗出现，导航关闭 */
      }
    }
  },
}