
import { createWebSocket, sendWebSocket, closeWebSocket } from "@/utils/socket";
import renderMyMarker from "@/class/DomMarker.js"
import { speak } from "@/api/operationmini.js"
export default {
  data: () => {
    return {
      singleAgentId: '1863782130367070208',
      chatMarkerList: [],
      mockQuestions: {
        index: 0,
        list: [
          '飞极迦先以欢快的语气给我打个招呼，然后再介绍一下自己吧！',
          "介绍一下阿派朗乐园#拟人语气#",
          "飞极迦的活动有哪些？#拟人语气#",
          "阿派朗的活动有哪些？#拟人语气#",
          // "飞极迦都有哪些票",
          // "阿派朗都有哪些票",
          // "介绍一下绿心公园",
          // "公园有哪些票？"
        ],
        stop: false
      },
      currentChat: {
        marker: null,
        id: '',
        question: '',
        answer: '',
        stop: false
      },
      audio: null,
      source: null,
      context: null
    }
  },
  mounted() {
    this.$confirm('需要您授权播放声音，以正常使用对话功能', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const contextClass = window.AudioContext = window.AudioContext || window.webkitAudioContext || window.mozAudioContext || window.msAudioContext;
      this.context = new contextClass();
      localStorage.setItem('audioContent', true)
    }).catch(() => {
      this.$message({
        type: 'info',
        message: '您已取消授权'
      });
    });
    
    const token = localStorage.getItem("token")
    //监听initMap函数执行完成
    this.$on('poiRender', (poiList) => {
      if (this.markerLayer && this.markerLayer.length > 0) {
        if (this.chatMarkerList.length > 0) {
          // 当前不存在的点位
          const newMarkers = poiList.filter(item => this.markerLayer.findIndex(i => i.data.id == item.id) == -1)
          // 存在的AI智能体点位
          const chatMarkers = this.markerLayer.filter(item => this.chatMarkerList.findIndex(i => i == item.data.id) > -1)
          // 存在的普通点位
          const markers = this.markerLayer.filter(item => this.chatMarkerList.findIndex(i => i == item.data.id) == -1)
          const markerLayer = renderMyMarker(newMarkers, this.TXMap, this.tMap)
          this.markerLayer = markerLayer.concat(chatMarkers).concat(markers)
          console.log(this.markerLayer);
        } else {
          this.markerLayer.map(item => item.destroy())
          this.markerLayer = []
          this.markerLayer = renderMyMarker(poiList, this.TXMap, this.tMap)
        }
      } else {
        this.markerLayer = renderMyMarker(poiList, this.TXMap, this.tMap)
      }
    })
    this.$on('initMap', () => {
      createWebSocket({ url: `wss://test.dreeck.com:11443/ddagentv2/ws/info?access_token=${token.split(' ')[1]}` }, this.receiveMsg)
      setTimeout(() => { this.dialogue() }, 3000)
    })
  },
  beforeDestroy() {
    closeWebSocket()
    this.stopAudio()
  },
  methods: {
    // 用户向智能体发送消息
    chatSend(e) {
      console.log(e);
      this.mockQuestions.stop = true
      this.currentChat.stop = true
      this.source.stop();
      this.sendMsg('stop')
      setTimeout(() => {
        this.sendMsg(e)
        this.chatMarkerList = []
        this.geoIndexAll()
      }, 1000)
    },
    // 对话
    dialogue() {
      this.sendMsg(this.mockQuestions.list[this.mockQuestions.index])
      this.currentChat.question = this.mockQuestions.list[this.mockQuestions.index]
      this.mockQuestions.index++
    },
    // 数据接收
    async receiveMsg(res) {
      console.log(JSON.parse(res));
      let data = JSON.parse(res)
      if (data.type == 'chat') {
        this.stopType = true
        this.answerMsgData = JSON.parse(res)
        if (data.text.msg.indexOf('dreamdeck') > -1) {
          console.log('开始对话', data.text.msg);
          this.currentChat.id = data.text.msgId
        }
        if (data.text.msg.indexOf('<Finish>') > -1) {
          let text = data.text.msg.replaceAll('<Finish>', '')
          // console.log('结束对话', text);
          if (text.includes('{') && text.includes('}')) {
            let obj = JSON.parse(text)
            console.log('接收到的地图AI智能体消息======', obj);
            if (Object.keys(obj).length > 1) {
              Object.keys(obj).map(async (item, index) => {
                console.log(item)
                setTimeout(() => { this.renderChatMarkers({ [item]: obj[item] }, data.text.msgId) }, index * 4000)
              })
            } else {
              this.renderChatMarkers(obj, data.text.msgId)
            }
          } else {
            await this.speak(text.replace(/\（.*?\）/g, ''), data.text.msgId)
            this.currentChat.answer = text
          }
        }
      }
    },
    renderChatMarkers(chatMarkers, msgId) {
      console.log('renderChatMarkers======', chatMarkers);
      console.log('this.markerLayer======', this.markerLayer);
      Object.keys(chatMarkers).forEach(async (key) => {
        const chatMarker = this.markerLayer.find(item => item.data.id == key)
        console.log('匹配到的地图AI智能', chatMarker);
        if (chatMarker) {
          if (this.currentChat.marker) {
            this.removeChatMarkerContent(this.currentChat.marker)
            this.currentChat.marker = chatMarker
          }
          if (!this.mockQuestions.stop) {
            if (this.mockQuestions.index == 1 && this.mockQuestions.index != this.mockQuestions.list.length) {
              chatMarkers[key] = chatMarkers[key] + '阿派朗别摸鱼了，你也介绍一下自己吧！'
            }
            if (this.mockQuestions.index == 2 && this.mockQuestions.index != this.mockQuestions.list.length) {
              chatMarkers[key] = chatMarkers[key] + '飞极迦，你都准备了什么好玩的活动呀？'
            }
            if (this.mockQuestions.index == 3 && this.mockQuestions.index != this.mockQuestions.list.length) {
              chatMarkers[key] = chatMarkers[key] + '阿派朗，你有什么好玩的活动哇？'
            }
          }
          console.log('chatMarkers[key]==============================', chatMarkers[key]);
          this.updateMarker(chatMarker, chatMarkers[key])
          this.chatMarkerList.push(Number(chatMarker.data.id))
          await this.speak(typeof chatMarkers[key] == 'object' ? chatMarkers[key] = JSON.stringify(chatMarkers[key]) : chatMarkers[key].replace(/\（.*?\）/g, ''), msgId)

        }
      })
    },
    updateMarker(chatMarker, msg) {
      console.log('接收到的消息======', msg);
      const container = chatMarker.dom;
      const icon = container.children[2]
      const name = container.children[3];
      container.style.transition = 'width 0.4s ease-in-out, height 0.4s ease-in-out,left 0.4s ease-in-out, top 0.4s ease-in-out'
      container.style.height = '70px'
      container.style.width = '300px'
      container.style.zIndex = parseInt(this.mockQuestions.index) + 100;
      container.setAttribute('id', chatMarker.data.id)
      icon.style.height = '120px';
      icon.style.width = '120px';
      icon.style.position = 'absolute'
      icon.style.bottom = '0px';
      icon.style.left = '-20px';
      name.style.marginLeft = '95px';
      name.style.width = '230px';
      name.style.display = 'block';
      if (typeof msg == 'object') {
        console.log(JSON.stringify(msg));
        name.innerText = JSON.stringify(msg)
      } else {
        name.innerText = msg;
      }
      console.log(icon);
      if (chatMarker.data.id == '37763') {
        icon.src = require("@/assets/icon/fjj.png")
      } else if (chatMarker.data.id == '37703') {
        icon.src = require("@/assets/icon/apl.png")
      } else if (chatMarker.data.id == '41186') {
        icon.src = require("@/assets/icon/lvxin.png")
      }
      console.log('点位更新=====', chatMarker.data.id, name.innerText);
      chatMarker.updateDOM()
      this.currentChat.marker = chatMarker
      setTimeout(() => {
        const center = this.tMap.getCenter()
        this.tMap.setCenter(new this.TXMap.LatLng(center.getLat() - 0.000001, center.getLng() - 0.000001))
        // const utterance = new SpeechSynthesisUtterance(msg); // 创建语音合成实例
        // utterance.lang = 'zh-CN'; // 设置语言为中文
        // utterance.volume = 1;      // 设置音量（0 到 1）
        // utterance.rate = 1;        // 设置语速（0.1 到 10）
        // utterance.pitch = 1;       // 设置音调（0 到 2）
        // const synth = window.speechSynthesis
        // synth.speak(utterance); // 播放语音
        // // 监听语音播放完成
        // utterance.onend = () => {
        //   if (!this.mockQuestions.stop) {
        //     setTimeout(() => {
        //       this.dialogue()
        //     }, 4000)
        //   }
        // };
      }, 300);

    },
    clearChatMarkers() {
      setTimeout(() => {
        this.chatMarkerList = []
        this.getPoiTypeAll()
      }, 10000)
    },
    sendMsg(msg) {
      if (msg == 'stop') {
        sendWebSocket({
          type: 'chat',
          msg: {
            msgStop: true,
            cache: false,
            description: '<Finish>',
            logId: this.currentChat.id,
            singleAgentId: this.singleAgentId,
          }
        })
        return
      }
      sendWebSocket({
        type: 'chat',
        msg: {
          description: msg,
          cache: false,
          singleAgentId: this.singleAgentId,
          msgStop: false
        }
      })
    },
    removeChatMarkerContent(chatMarker) {
      console.log('removeChatMarkerContent======', chatMarker);
      const container = chatMarker.dom;
      const icon = container.children[2];
      if (chatMarker.data.id == '37763') {
        icon.src = require("@/assets/icon/fjj2.png")
      } else if (chatMarker.data.id == '37703') {
        icon.src = require("@/assets/icon/apl2.png")
      } else if (chatMarker.data.id == '41186') {
        icon.src = require("@/assets/icon/lvxin.png")
      }
      const name = container.children[3];
      container.style.width = '70px'
      name.innerText = ''
      name.style.marginLeft = '0px';
      // name.style.width = '0px';
      name.style.display = 'none';
      let pixel = this.tMap.projectToContainer(chatMarker.position); // 经纬度坐标转容器像素坐标
      let left = pixel.getX() - chatMarker.dom.clientWidth / 2 + 'px';
      let top = pixel.getY() - chatMarker.dom.clientHeight + 'px';
      // 使用top/left将DOM元素定位到指定位置
      container.style.top = top;
      container.style.left = left;
      chatMarker.updateDOM()
      setTimeout(() => {
        const center = this.tMap.getCenter()
        this.tMap.setCenter(new this.TXMap.LatLng(center.getLat() + 0.000001, center.getLng() + 0.000001))
      }, 300)
    },
    async speak(msg, id) {
      const res = await speak({
        message: msg,
        audioName: id,
        voiceName: 'vixy',
        speed: '70',
        volume: 100,
        bucketName: 'operationminifile'
      })
      console.log('语音合成成功======', res.data);
      const audioUrl = process.env.VUE_APP_BASE_API + '/admin/file/' + res.data
      this.loadAudioFile(audioUrl)

    },
    loadAudioFile(url) {
      const that = this
      var xhr = new XMLHttpRequest(); //通过XHR下载音频文件
      xhr.open('GET', url, true);
      xhr.responseType = 'arraybuffer';
      xhr.onload = function (e) { //下载完成
        that.initSound(this.response);
      };
      xhr.send();
    },
    initSound(arrayBuffer) {
      const that = this
      this.context.decodeAudioData(arrayBuffer, function (buffer) { //解码成功时的回调函数
        that.playSound(buffer);
      }, function (e) { //解码出错时的回调函数
        console.log('404', e);
      });
    },
    playSound(audioBuffer) {
      try {
        console.log('音频已加载并准备播放', this.source, audioBuffer);
        const that = this
        this.source = this.context.createBufferSource();
        this.source.buffer = audioBuffer;
        // this.source.loop = true;
        this.source.connect(this.context.destination);
        this.source.start(0) //立即播放
        // 监听音频播放完成
        this.source.onended = function () {
          if (!that.mockQuestions.stop) that.dialogue();
          if (that.mockQuestions.index >= that.mockQuestions.list.length) {
            console.log("模拟对话结束=========================");
            that.clearChatMarkers()
            that.mockQuestions.stop = true
            /* The above code is setting the `buffer` property of the `source` object to `null` in
            JavaScript. */
            // this.source.buffer = null
          }
        };
      } catch (error) {
        console.error(error);
      }
    },
    onEnded() {
      if (!this.mockQuestions.stop) this.dialogue();
      if (this.mockQuestions.index >= this.mockQuestions.list.length) {
        console.log("模拟对话结束=========================");
        this.clearChatMarkers()
        this.mockQuestions.stop = true
      }
    },
  }
}