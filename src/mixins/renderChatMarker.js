


import renderMyMarker from "@/class/DomMarker.js"
import { createWebSocket, sendWebSocket, closeWebSocket } from "@/utils/socket";
export default {
  data: () => {
    return {
      currentChat: {
        marker: null,
        id: '',
        question: '',
        answer: '',
        stop: false
      },
    }
  },
  mounted() {
    this.$on('initMap', () => {
      createWebSocket({ url: `wss://test.dreeck.com:11443/ddagentv2/ws/info?access_token=${token.split(' ')[1]}` }, this.receiveMsg)
      setTimeout(() => { this.dialogue() }, 3000)
    })
    //监听initMap函数执行完成
    this.$on('poiRender', (poiList) => {
      console.log(poiList, 'poiList');
      this.markerLayer.map(item => item.destroy())
      this.markerLayer = []
      const markerLayer = renderMyMarker(poiList, this.TXMap, this.tMap)
    })
  },
  beforeDestroy() {

  },
  methods: {
    // 对话
    dialogue() {
      this.sendMsg(this.mockQuestions.list[this.mockQuestions.index])
      this.currentChat.question = this.mockQuestions.list[this.mockQuestions.index]
      this.mockQuestions.index++
    },
    renderMyMarker() {

    },
    // 用户向智能体发送消息
    chatSend(e) {
      console.log(e);
      this.mockQuestions.stop = true
      this.currentChat.stop = true
      console.dir(this.audio);
      this.stopAudio()
      this.audio = null
      this.sendMsg('stop')
      setTimeout(() => {
        this.sendMsg(e)
        this.chatMarkerList = []
        this.geoIndexAll()
      }, 1000)
    },
  }
}