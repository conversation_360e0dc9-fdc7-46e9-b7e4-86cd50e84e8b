

import { speak } from "@/api/operationmini.js"
export default {
  data: () => {
    return {
      source: null,
      context: null,
      timer1: null,
      timer2: null,
      timer3: null,
    }
  },
  mounted() {
    if (localStorage.getItem("sysTenantId") != '11048' && localStorage.getItem("sysTenantId") != '10205') return;
    this.$confirm('需要您授权播放声音，以正常使用对话功能', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const contextClass = window.AudioContext = window.AudioContext || window.webkitAudioContext || window.mozAudioContext || window.msAudioContext;
      this.context = new contextClass();
      localStorage.setItem('audioContent', true)
      setTimeout(() => { this.dialogue() }, 3000)
    }).catch(() => {
      this.$message({
        type: 'info',
        message: '您已取消授权'
      });
    });
  },
  beforeDestroy() {
    if (this.source) this.source.stop();
    this.currentChat.multiple = false
    this.multipleChat.list = []
    this.multipleChat.index = 0
    clearTimeout(this.timer1)
    clearTimeout(this.timer2)
    clearTimeout(this.timer3)
  },
  methods: {
    async speak(msg, id, sound) {
      const res = await speak({
        message: msg.replace(/\（.*?\）/g, ''),
        audioName: id,
        voiceName: sound,
        speed: '70',
        volume: 100,
        bucketName: 'operationminifile'
      })
      console.log('语音合成成功======', res.data);
      const audioUrl = process.env.VUE_APP_BASE_API + '/admin/file/' + res.data
      this.loadAudioFile(audioUrl)
    },
    loadAudioFile(url) {
      const that = this
      var xhr = new XMLHttpRequest(); //通过XHR下载音频文件
      xhr.open('GET', url, true);
      xhr.responseType = 'arraybuffer';
      xhr.onload = function (e) { //下载完成
        that.initSound(this.response);
      };
      xhr.send();
    },
    initSound(arrayBuffer) {
      const that = this
      this.context.decodeAudioData(arrayBuffer, function (buffer) { //解码成功时的回调函数
        that.playSound(buffer);
      }, function (e) { //解码出错时的回调函数
        console.log('404', e);
      });
    },
    playSound(audioBuffer) {
      try {
        console.log('音频已加载并准备播放', this.source, audioBuffer);
        const that = this
        this.source = this.context.createBufferSource();
        this.source.buffer = audioBuffer;
        // this.source.loop = true;
        this.source.connect(this.context.destination);
        this.source.start(0) //立即播放
        // 监听音频播放完成
        this.source.onended = that.onEnded
      } catch (error) {
        console.error(error);
      }
    },
    onEnded() {
      console.log('播放完成', this.currentChat.chatMarker);
      if (!this.mockQuestions.stop) this.dialogue();
      if (this.currentChat.chatMarker != null) {
        this.updateImgMarker()
        this.timer1 = setTimeout(() => { this.currentChat.chatMarker.chatDomMarker.setMap(null) }, 100)
      }
      if (this.mockQuestions.index > this.mockQuestions.list.length) {
        this.timer2 = setTimeout(() => {
          this.clearChatMarkers()
        }, 2000);
        console.log("模拟对话结束=========================");
        this.mockQuestions.stop = true
      }
      if (this.currentChat.multiple) {
        if (this.multipleChat.index == this.multipleChat.list.length) {
          this.multipleChat.index = 0
          this.currentChat.multiple = false
          this.clearChatMarkers()
          return
        }
        this.timer3 = setTimeout(() => {
          this.sendMsg(this.multipleChat.list[this.multipleChat.index].question)
        }, 300);
      }
    },
  }
}