/*
 * @Author: sunfeiyue <EMAIL>
 * @Date: 2023-12-21 12:05:23
 * @LastEditors: sunfeiyue <EMAIL>
 * @LastEditTime: 2023-12-28 17:02:00
 * @FilePath: \dd-mini-operation-saas-park-map\src\api\operationbase.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import fetch from "@/utils/request"



/** 获取所有主题 */
export function getThemeList(params) {
	return fetch({
		url: '/operationbase/operation/dict/item/all',
		method: 'get',
		params
	})
}
// 根据组织id查询
export function getOperationTenantInfo(id) {
	return fetch({
		url: `/operationbase/operation/tenant/info/${id}`,
		method: 'get'
	})
}

// 根据组织id查询
export function getByTenantId(params) {
	return fetch({
		url: `/operationbase/operation/tenant/getByTenantId`,
		method: 'get',
		params
	})
}

// 活动推荐  
export function getOperationActivityAll(params) {
	return fetch({
		url: `/operationbase/operation/activity/all`,
		method: 'get',
		params
	})
}

// 字典 全部  
export function getDictItemAll(params) {
	return fetch({
		url: `/operationbase/operation/dict/item/all`,
		method: 'get',
		params
	})
}