import fetch from "@/utils/request"




// 类型全部
export function getPoiTypeAll(params) {
	return fetch({
		url: `/operationmini/poi/type/all`,
		method: 'get',
		params
	})
}


// 游玩全部根据视图 
export function geoIndexAll(params) {
	return fetch({
		url: `/operationmini/poi/geoIndexAll`,
		method: 'get',
		params
	})
}

// 游线推荐  
export function getTourLineAll(params) {
	return fetch({
		url: `/operationmini/tour/line/all`,
		method: 'get',
		params
	})
}

// poi 全部  /operationmini/poi/all
export function getPoiAll(params) {
	return fetch({
		url: `/operationmini/poi/all`,
		method: 'get',
		params
	})
}

// 金刚区  
export function getKongAll(params) {
	return fetch({
		url: `/operationmini/kong/all`,
		method: 'get',
		params
	})
}

// 系统公告
export function getAnnouncementAll(params) {
	return fetch({
		url: `/operationmini/announce/all`,
		method: 'get',
		params
	})
}
// 赏花模板-查询月份operationmini/tour/line/selectInfoByTypeAndMonth
export function getPreferredList(params) {
	return fetch({
		url: `/operationmini/tour/line/selectInfoByTypeAndMonth`,
		method: 'get',
		params
	})
}
export function getactivityAll(params) {
	return fetch({
		url: `/operationmarketing/market/activity/all`,
		method: 'get',
		params
	})
}

// 解密URl
export function getWxAuth(params) {
	return fetch({
		url: `/operationbase/wx/util/getSgtureUrl`,
		method: 'get',
		params
	})
}

export function speak(data) {
	return fetch({
		url: `/ddagentv2/knowledge/single/agent/dubbing`,
		method: 'post',
		headers: {
			'content-type': 'application/x-www-form-urlencoded'
		},
		data
	})
}

