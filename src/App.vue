<template>
	<div id="app">
		<router-view />
	</div>
</template>
<script>
export default {
	data() {
		return {

		}
	},
	created() {
		try {
			let token = window.location.href.split('?')[1].split('&')[0].split('=')[1]
			let userId = window.location.href.split('?')[1].split('&')[1].split('=')[1]
			let sysTenantId = window.location.href.split('?')[1].split('&')[2].split('=')[1]
			// let parkId = window.location.href.split('?')[1].split('&')[3].split('=')[1]
			window.localStorage.setItem('token', 'Bearer ' + token)
			window.localStorage.setItem('userId', userId)
			window.localStorage.setItem('sysTenantId', sysTenantId)

			// window.localStorage.setItem('parkId', parkId)
		} catch (error) {

		}
	}
}
</script>
<style>
* {
	margin: 0;
	padding: 0;
}

html,
body,
#app {
	width: 100%;
	height: 100%;
}

.amap-logo,
.amap-copyright {
	display: none !important;
}

/* 隐藏marker icon */
.amap-icon {
	position: relative !important;
}

/* 因需要改变地图自带样式 须使用css 最高优先级 */
.amap-marker {
	overflow: hidden;
}

.amap-marker .amap-marker-label {
	color: #636363;
	border: none !important;
	padding: 6px 12px !important;
	box-sizing: border-box;
	border-radius: 14px !important;
	background: none !important;
	width: 100% !important;
	position: absolute !important;
	z-index: 99 !important;
	top: 15% !important;
	left: auto !important;
	text-align: center !important;
	/* transition: all 1s !important; */
	/* overflow: hidden !important;
		white-space: nowrap !important;
		text-overflow: ellipsis !important; */
}

/* .amap-marker .amap-marker-label::after{
		position: absolute;
		left: 50%;
		bottom: -18px;
		border-top: 10px solid #fff;
		border-left: 10px solid #ffffff00;
		border-right: 10px solid #ffffff00;
		border-bottom: 10px solid #ffffff00;
		display: block;
		content: '';
		margin: 0 0 0 -10px;
	} */

.amap-geolocation-con {
	left: auto !important;
	right: 10px !important;
	bottom: 160px !important;
	box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
	border-radius: 30px;
	z-index: 9 !important;
}

.amap-geolocation-con .amap-geo {
	border: none !important;
	background: url('https://www.dreamdeck.cn:10443/app/icons/southtwoloop/parkMapLocation.png') no-repeat 100%/100% !important;
	width: 40px !important;
	height: 46px !important;
}

.el-message-box {
	width: 80% !important;
}

.chat-dom-marker::after {
	content: "";
	position: absolute;
	width: 102%;
	height: 105%;
	border-radius: 12px;
	border: 2px solid #ffffff;
	left: -4px;
	top: -4px;
	z-index: -1;
}
</style>
