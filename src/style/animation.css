

/* marker跳动的动画 */
.markerBounce {
  animation: bounce 0.5s infinite ease-in-out alternate;
}

/* marker飞入的动画 */
.markerFlash {
  animation: flash 0.5s ease-in 1 normal forwards;
}

/* 跳动的动画 */
@keyframes bounce {
  0% {
    transform: translate(0, 0);
  }

  100% {
    transform: translate(0, -50px);
  }
}

/* 飞入的动画 */
@keyframes flash {
  0% {
    transform: translate(0, -200px);
  }

  100% {
    transform: translate(0, 0);
  }
}

/* 飞入的动画 */
@keyframes scene-in {
  0% {
    transform: translate(0, -200px);
  }

  100% {
    transform: translate(0, 0);
  }
}