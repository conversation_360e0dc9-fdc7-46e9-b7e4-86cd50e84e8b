{"name": "dd-mini-greenheart-map", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "deploy:dev": "cross-env ENV=development node scripts/deploy.js", "deploy:prod": "cross-env ENV=production node scripts/deploy.js"}, "dependencies": {"axios": "^1.4.0", "better-scroll": "^2.5.1", "core-js": "^3.6.5", "element-ui": "^2.15.13", "express": "^4.21.1", "js-audio-recorder": "^1.0.7", "lamejs": "^1.2.1", "reconnecting-websocket": "^4.4.0", "scrollboundary": "^1.0.3", "sockjs-client": "^1.6.1", "svgaplayerweb": "^2.3.2", "vconsole": "^3.15.1", "vue": "^2.6.11", "vue-amap": "^0.5.10", "vue-jsonp": "^2.0.0", "vue-router": "^3.2.0", "vue-seamless-scroll": "^1.1.23", "vuex": "^3.4.0", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.13", "@vue/cli-plugin-router": "~4.5.13", "@vue/cli-plugin-vuex": "~4.5.13", "@vue/cli-service": "~4.5.13", "chalk": "^4.1.2", "cross-env": "^7.0.3", "node-ssh": "^13.1.0", "ora": "^5.4.1", "sass": "1.32.13", "sass-loader": "^10.1.1", "swiper": "^3.4.2", "vue-awesome-swiper": "^3.1.3", "vue-template-compiler": "^2.6.11", "webpack": "^4.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}